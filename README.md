# Cybernet Magasin - نظام إدارة المتاجر المتكامل

![Cybernet Magasin](https://img.shields.io/badge/Version-1.0.0-blue.svg)
![License](https://img.shields.io/badge/License-Free-green.svg)
![Language](https://img.shields.io/badge/Language-Arabic-red.svg)

## 📋 نظرة عامة

**Cybernet Magasin** هو نظام إدارة متاجر شامل ومتكامل مصمم خصيصاً للمتاجر والشركات الصغيرة والمتوسطة. يوفر النظام جميع الأدوات اللازمة لإدارة المبيعات، المشتريات، المخزون، العملاء، والموردين بطريقة سهلة وفعالة.

## ✨ المميزات الرئيسية

### 🛒 نقطة البيع (POS)
- واجهة بيع سريعة وسهلة الاستخدام
- دعم الباركود والبحث السريع
- حساب الضرائب والخصومات تلقائياً
- طرق دفع متعددة (نقداً، على الحساب)
- طباعة الفواتير

### 📦 إدارة المخزون
- تتبع المنتجات والكميات
- تنبيهات المخزون المنخفض
- إدارة الفئات والتصنيفات
- تتبع تواريخ انتهاء الصلاحية
- تقييم المخزون بطرق متعددة

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع الديون والمدفوعات
- تاريخ المعاملات
- إدارة الحسابات الجارية

### 🚚 إدارة الموردين
- قاعدة بيانات الموردين
- تتبع المستحقات
- إدارة المشتريات
- تقارير الموردين

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- تقارير المخزون
- تقارير العملاء والديون
- تقارير الموردين والمستحقات
- التقرير المالي الشامل

### ⚙️ الإعدادات والتخصيص
- إعدادات الشركة والمعلومات
- تخصيص الثيم (فاتح/داكن)
- إعدادات الضرائب والعملة
- النسخ الاحتياطي التلقائي
- إدارة المستخدمين والأمان

## 🚀 التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **التخزين**: LocalStorage API
- **التصميم**: Neumorphism Design
- **الخطوط**: Google Fonts (Cairo)
- **الأيقونات**: Font Awesome 6
- **الطباعة**: CSS Print Media Queries

## 📁 هيكل المشروع

```
cybernet-magasin/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── style.css          # الأنماط الرئيسية
│   ├── themes.css         # أنماط الثيمات
│   └── print.css          # أنماط الطباعة
├── js/
│   ├── app.js             # التطبيق الرئيسي
│   ├── main.js            # ملف التهيئة
│   ├── auth.js            # نظام المصادقة
│   ├── database.js        # قاعدة البيانات
│   ├── utils.js           # الأدوات المساعدة
│   ├── dashboard.js       # لوحة المعلومات
│   ├── sales.js           # نظام المبيعات
│   ├── products.js        # إدارة المنتجات
│   ├── customers.js       # إدارة العملاء
│   ├── suppliers.js       # إدارة الموردين
│   ├── purchases.js       # نظام المشتريات
│   ├── reports.js         # نظام التقارير
│   ├── settings.js        # الإعدادات
│   ├── print.js           # نظام الطباعة
│   ├── notifications.js   # نظام الإشعارات
│   └── backup.js          # نظام النسخ الاحتياطي
└── README.md              # ملف التوثيق
```

## 🔧 التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب خادم ويب (يعمل محلياً)

### خطوات التثبيت
1. قم بتحميل المشروع أو استنساخه
2. افتح ملف `index.html` في المتصفح
3. استخدم كلمة المرور الافتراضية: `1234`

### التشغيل على خادم محلي (اختياري)
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# باستخدام PHP
php -S localhost:8000
```

## 🔐 المصادقة والأمان

- **كلمة المرور الافتراضية**: `1234`
- **اسم المستخدم**: `admin`
- يمكن تغيير كلمة المرور من الإعدادات
- تشفير بسيط للبيانات الحساسة
- جلسات آمنة مع انتهاء صلاحية تلقائي

## 📱 التوافق

- **المتصفحات**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **الأجهزة**: أجهزة سطح المكتب والأجهزة اللوحية
- **الشاشات**: تصميم متجاوب يدعم جميع أحجام الشاشات
- **اللغة**: واجهة عربية بالكامل مع دعم RTL

## 🎨 التخصيص

### تغيير الألوان
يمكن تخصيص ألوان النظام من خلال تعديل متغيرات CSS في ملف `themes.css`:

```css
:root {
    --primary-color: #730C02;
    --secondary-color: #F2490C;
    --accent-color: #1DDF2;
    --info-color: #0F8DBF;
    --dark-color: #0A3B59;
}
```

### إضافة ثيمات جديدة
يمكن إضافة ثيمات جديدة بإنشاء فئات CSS جديدة في ملف `themes.css`.

## 📊 البيانات والتخزين

- **التخزين المحلي**: LocalStorage API
- **حجم البيانات**: يدعم آلاف السجلات
- **النسخ الاحتياطي**: تلقائي ويدوي
- **التصدير**: JSON, CSV
- **الاستيراد**: JSON

## 🔄 النسخ الاحتياطي

### النسخ التلقائي
- يمكن تفعيله من الإعدادات
- فترات قابلة للتخصيص (يومي، أسبوعي، شهري)
- حفظ تلقائي في التخزين المحلي

### النسخ اليدوي
- إنشاء نسخة احتياطية فورية
- تحميل ملف JSON
- استعادة من ملف محفوظ

## 📈 التقارير المتاحة

1. **تقرير المبيعات**: إحصائيات المبيعات اليومية والشهرية
2. **تقرير المخزون**: حالة المخزون والمنتجات
3. **تقرير العملاء**: الديون والمدفوعات
4. **تقرير الموردين**: المستحقات والمشتريات
5. **التقرير المالي**: الأرباح والخسائر
6. **تقرير المشتريات**: تفاصيل المشتريات والتكاليف

## 🛠️ الصيانة والدعم

### تحديث النظام
- تحقق من الإصدارات الجديدة دورياً
- احتفظ بنسخة احتياطية قبل التحديث
- اتبع تعليمات التحديث المرفقة

### استكشاف الأخطاء
- تحقق من وحدة تحكم المتصفح للأخطاء
- امسح ذاكرة التخزين المؤقت إذا لزم الأمر
- استعد نسخة احتياطية في حالة فقدان البيانات

## 📞 الدعم والمساعدة

- **المطور**: salim
- **البريد الإلكتروني**: [البريد الإلكتروني]
- **الدعم الفني**: متوفر للمساعدة في التثبيت والاستخدام

## 📄 الترخيص

هذا المشروع مجاني ومفتوح المصدر. يمكن استخدامه وتعديله بحرية للأغراض التجارية وغير التجارية.

## 🙏 شكر وتقدير

- شكر خاص لجميع المساهمين في تطوير هذا النظام
- شكر لمجتمع المطورين العرب على الدعم والمساعدة
- شكر لجميع المستخدمين على ملاحظاتهم واقتراحاتهم

## 🔮 التطوير المستقبلي

- دعم قواعد البيانات السحابية
- تطبيق جوال مصاحب
- تكامل مع أنظمة الدفع الإلكتروني
- تقارير متقدمة وذكية
- دعم متعدد المستخدمين
- واجهة برمجة تطبيقات (API)

---

**Cybernet Magasin** - نظام إدارة المتاجر الذكي والمتكامل 🏪✨
