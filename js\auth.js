/**
 * Cybernet Magasin - نظام المصادقة
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class AuthSystem {
    constructor() {
        this.isLoggedIn = false;
        this.sessionKey = 'cm_session';
        this.maxAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15 دقيقة
        this.init();
    }

    // تهيئة نظام المصادقة
    init() {
        this.checkSession();
        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        const loginForm = DOMUtils.$('#login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // مستمع لتبديل إظهار كلمة المرور
        window.togglePassword = () => {
            const passwordInput = DOMUtils.$('#password');
            const toggleBtn = DOMUtils.$('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        };

        // مستمع لتسجيل الخروج
        window.logout = () => {
            this.logout();
        };
    }

    // التحقق من الجلسة الحالية
    checkSession() {
        const session = StorageUtils.get(this.sessionKey);
        if (session && this.isValidSession(session)) {
            this.isLoggedIn = true;
            this.showApp();
        } else {
            this.isLoggedIn = false;
            this.showLogin();
        }
    }

    // التحقق من صحة الجلسة
    isValidSession(session) {
        if (!session || !session.timestamp || !session.token) {
            return false;
        }

        // التحقق من انتهاء صلاحية الجلسة (24 ساعة)
        const sessionAge = Date.now() - session.timestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
        
        return sessionAge < maxAge;
    }

    // معالجة تسجيل الدخول
    async handleLogin() {
        const passwordInput = DOMUtils.$('#password');
        const errorDiv = DOMUtils.$('#login-error');
        const submitBtn = DOMUtils.$('#login-form button[type="submit"]');
        
        // إخفاء رسائل الخطأ السابقة
        this.hideError();
        
        // التحقق من القفل
        if (this.isAccountLocked()) {
            this.showError('تم قفل الحساب مؤقتاً. حاول مرة أخرى لاحقاً.');
            return;
        }

        const password = passwordInput.value.trim();
        
        // التحقق من كلمة المرور
        if (!password) {
            this.showError('يرجى إدخال كلمة المرور');
            passwordInput.focus();
            return;
        }

        // تعطيل الزر أثناء المعالجة
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';

        try {
            // محاكاة تأخير للتحقق
            await Utils.delay(1000);

            if (this.verifyPassword(password)) {
                // نجح تسجيل الدخول
                this.onLoginSuccess();
                passwordInput.value = '';
            } else {
                // فشل تسجيل الدخول
                this.onLoginFailure();
                passwordInput.value = '';
                passwordInput.focus();
            }
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            this.showError('حدث خطأ غير متوقع. حاول مرة أخرى.');
        } finally {
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> دخول';
        }
    }

    // التحقق من كلمة المرور
    verifyPassword(password) {
        const settings = db.getSettings();
        const storedPassword = settings.system?.password;
        
        if (!storedPassword) {
            // كلمة المرور الافتراضية
            return password === '1234';
        }

        // فك تشفير كلمة المرور المحفوظة
        const decryptedPassword = Utils.simpleDecrypt(storedPassword);
        return password === decryptedPassword;
    }

    // عند نجاح تسجيل الدخول
    onLoginSuccess() {
        this.isLoggedIn = true;
        this.createSession();
        this.resetLoginAttempts();
        this.showApp();
        
        // إظهار رسالة ترحيب
        setTimeout(() => {
            this.showWelcomeMessage();
        }, 500);
    }

    // عند فشل تسجيل الدخول
    onLoginFailure() {
        this.incrementLoginAttempts();
        const attempts = this.getLoginAttempts();
        const remaining = this.maxAttempts - attempts;
        
        if (remaining > 0) {
            this.showError(`كلمة المرور غير صحيحة. المحاولات المتبقية: ${remaining}`);
        } else {
            this.lockAccount();
            this.showError('تم تجاوز عدد المحاولات المسموحة. تم قفل الحساب مؤقتاً.');
        }
    }

    // إنشاء جلسة جديدة
    createSession() {
        const session = {
            token: this.generateSessionToken(),
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            ip: 'local'
        };
        
        StorageUtils.set(this.sessionKey, session);
    }

    // توليد رمز الجلسة
    generateSessionToken() {
        return btoa(Date.now() + Math.random().toString(36));
    }

    // تسجيل الخروج
    logout() {
        this.isLoggedIn = false;
        StorageUtils.remove(this.sessionKey);
        this.showLogin();
        
        // إظهار رسالة تسجيل الخروج
        setTimeout(() => {
            this.showLogoutMessage();
        }, 300);
    }

    // إظهار شاشة تسجيل الدخول
    showLogin() {
        DOMUtils.hide('#app-container');
        DOMUtils.show('#login-screen');
        
        // التركيز على حقل كلمة المرور
        setTimeout(() => {
            const passwordInput = DOMUtils.$('#password');
            if (passwordInput) {
                passwordInput.focus();
            }
        }, 100);
    }

    // إظهار التطبيق
    showApp() {
        DOMUtils.hide('#login-screen');
        DOMUtils.show('#app-container');
        
        // تحديث واجهة المستخدم
        if (window.app && typeof window.app.updateUI === 'function') {
            window.app.updateUI();
        }
    }

    // إظهار رسالة خطأ
    showError(message) {
        const errorDiv = DOMUtils.$('#login-error');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                this.hideError();
            }, 5000);
        }
    }

    // إخفاء رسالة الخطأ
    hideError() {
        const errorDiv = DOMUtils.$('#login-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    // زيادة عدد محاولات تسجيل الدخول
    incrementLoginAttempts() {
        const attempts = this.getLoginAttempts() + 1;
        StorageUtils.set('cm_login_attempts', {
            count: attempts,
            timestamp: Date.now()
        });
    }

    // الحصول على عدد محاولات تسجيل الدخول
    getLoginAttempts() {
        const data = StorageUtils.get('cm_login_attempts', { count: 0, timestamp: 0 });
        
        // إعادة تعيين المحاولات إذا مر أكثر من ساعة
        if (Date.now() - data.timestamp > 60 * 60 * 1000) {
            this.resetLoginAttempts();
            return 0;
        }
        
        return data.count;
    }

    // إعادة تعيين محاولات تسجيل الدخول
    resetLoginAttempts() {
        StorageUtils.remove('cm_login_attempts');
        StorageUtils.remove('cm_account_locked');
    }

    // قفل الحساب
    lockAccount() {
        StorageUtils.set('cm_account_locked', {
            timestamp: Date.now(),
            duration: this.lockoutTime
        });
    }

    // التحقق من قفل الحساب
    isAccountLocked() {
        const lockData = StorageUtils.get('cm_account_locked');
        if (!lockData) return false;
        
        const elapsed = Date.now() - lockData.timestamp;
        if (elapsed >= lockData.duration) {
            // انتهت مدة القفل
            StorageUtils.remove('cm_account_locked');
            this.resetLoginAttempts();
            return false;
        }
        
        return true;
    }

    // تغيير كلمة المرور
    changePassword(currentPassword, newPassword) {
        if (!this.verifyPassword(currentPassword)) {
            return { success: false, message: 'كلمة المرور الحالية غير صحيحة' };
        }

        if (newPassword.length < 4) {
            return { success: false, message: 'كلمة المرور يجب أن تكون 4 أحرف على الأقل' };
        }

        // تشفير كلمة المرور الجديدة
        const encryptedPassword = Utils.simpleEncrypt(newPassword);
        
        // حفظ كلمة المرور الجديدة
        const settings = db.getSettings();
        settings.system = settings.system || {};
        settings.system.password = encryptedPassword;
        
        if (db.saveSettings(settings)) {
            return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };
        } else {
            return { success: false, message: 'فشل في حفظ كلمة المرور الجديدة' };
        }
    }

    // إظهار رسالة ترحيب
    showWelcomeMessage() {
        if (window.showNotification) {
            window.showNotification('مرحباً بك في نظام Cybernet Magasin', 'success');
        }
    }

    // إظهار رسالة تسجيل الخروج
    showLogoutMessage() {
        if (window.showNotification) {
            window.showNotification('تم تسجيل الخروج بنجاح', 'info');
        }
    }

    // التحقق من حالة تسجيل الدخول
    isAuthenticated() {
        return this.isLoggedIn;
    }

    // الحصول على معلومات الجلسة
    getSessionInfo() {
        if (!this.isLoggedIn) return null;
        
        const session = StorageUtils.get(this.sessionKey);
        if (!session) return null;
        
        return {
            loginTime: new Date(session.timestamp),
            sessionAge: Date.now() - session.timestamp,
            userAgent: session.userAgent
        };
    }

    // تجديد الجلسة
    refreshSession() {
        if (this.isLoggedIn) {
            const session = StorageUtils.get(this.sessionKey);
            if (session) {
                session.timestamp = Date.now();
                StorageUtils.set(this.sessionKey, session);
            }
        }
    }

    // حماية الصفحات
    requireAuth(callback) {
        if (this.isAuthenticated()) {
            callback();
        } else {
            this.showLogin();
        }
    }
}

// إنشاء مثيل من نظام المصادقة
const auth = new AuthSystem();

// تصدير نظام المصادقة للاستخدام العام
window.auth = auth;
