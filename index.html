<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cybernet Magasin - نظام إدارة نقاط البيع</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/print.css" media="print">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="light-theme">
    <!-- شاشة التحميل -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <h3>جاري تحميل النظام...</h3>
        </div>
    </div>

    <!-- شاشة تسجيل الدخول -->
    <div id="login-screen" class="login-screen hidden">
        <div class="login-container">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h1>Cybernet Magasin</h1>
                <p>نظام إدارة نقاط البيع الشامل</p>
            </div>
            <div class="login-form">
                <h2>تسجيل الدخول</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" id="password" placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="toggle-password" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        دخول
                    </button>
                </form>
                <div id="login-error" class="error-message"></div>
                <div class="login-footer">
                    <p>كلمة المرور الافتراضية: <strong>1234</strong></p>
                </div>
            </div>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="app-container" class="app-container hidden">
        <!-- شريط علوي -->
        <header class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">لوحة المعلومات</h1>
            </div>
            <div class="top-bar-right">
                <div class="theme-toggle">
                    <button onclick="toggleTheme()" title="تبديل الثيم">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
                <div class="notifications">
                    <button class="notification-btn" onclick="showNotifications()">
                        <i class="fas fa-bell"></i>
                        <span class="notification-count">0</span>
                    </button>
                </div>
                <div class="user-menu">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <span>المدير</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown">
                        <a href="#" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- القائمة الجانبية -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo-mini">
                    <i class="fas fa-store"></i>
                    <span>CM</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#" onclick="showPage('dashboard')" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة المعلومات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('sales')" class="nav-link">
                            <i class="fas fa-cash-register"></i>
                            <span>نقطة البيع</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('products')" class="nav-link">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('customers')" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('suppliers')" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>إدارة الموردين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('purchases')" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>المشتريات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('reports')" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPage('settings')" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content" id="main-content">
            <!-- صفحة لوحة المعلومات -->
            <div id="dashboard-page" class="page active">
                <!-- سيتم تحميل المحتوى من dashboard.js -->
            </div>

            <!-- صفحة نقطة البيع -->
            <div id="sales-page" class="page">
                <!-- سيتم تحميل المحتوى من sales.js -->
            </div>

            <!-- صفحة إدارة المنتجات -->
            <div id="products-page" class="page">
                <!-- سيتم تحميل المحتوى من products.js -->
            </div>

            <!-- صفحة إدارة العملاء -->
            <div id="customers-page" class="page">
                <!-- سيتم تحميل المحتوى من customers.js -->
            </div>

            <!-- صفحة إدارة الموردين -->
            <div id="suppliers-page" class="page">
                <!-- سيتم تحميل المحتوى من suppliers.js -->
            </div>

            <!-- صفحة المشتريات -->
            <div id="purchases-page" class="page">
                <!-- سيتم تحميل المحتوى من purchases.js -->
            </div>

            <!-- صفحة التقارير -->
            <div id="reports-page" class="page">
                <!-- سيتم تحميل المحتوى من reports.js -->
            </div>

            <!-- صفحة الإعدادات -->
            <div id="settings-page" class="page">
                <!-- سيتم تحميل المحتوى من settings.js -->
            </div>
        </main>
    </div>

    <!-- نافذة الإشعارات -->
    <div id="notifications-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإشعارات</h3>
                <button class="close-btn" onclick="closeModal('notifications-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="notifications-list">
                    <!-- سيتم تحميل الإشعارات هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirm-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirm-title">تأكيد العملية</h3>
            </div>
            <div class="modal-body">
                <p id="confirm-message">هل أنت متأكد من هذه العملية؟</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('confirm-modal')">إلغاء</button>
                <button class="btn btn-danger" id="confirm-btn">تأكيد</button>
            </div>
        </div>
    </div>

    <!-- ملفات JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/products.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/print.js"></script>
    <script src="js/backup.js"></script>
</body>
</html>