/* 
 * <PERSON>ber<PERSON> Magasin - ملف الثيمات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

/* الثيم المضيء (افتراضي) */
.light-theme {
    --bg-primary: #f5f7fa;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    
    /* الظلال للثيم المضيء */
    --shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --shadow-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    --shadow-hover: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff;
    --shadow-pressed: inset 4px 4px 8px #d1d9e6, inset -4px -4px 8px #ffffff;
}

/* الثيم الداكن */
.dark-theme {
    --bg-primary: #1a1d23;
    --bg-secondary: #25282e;
    --bg-card: #2d3139;
    --text-primary: #ffffff;
    --text-secondary: #adb5bd;
    --border-color: #495057;
    
    /* ألوان النصوص للثيم الداكن */
    --gray-100: #495057;
    --gray-200: #6c757d;
    --gray-300: #adb5bd;
    --gray-400: #ced4da;
    --gray-500: #dee2e6;
    --gray-600: #e9ecef;
    --gray-700: #f8f9fa;
    --gray-800: #ffffff;
    --gray-900: #ffffff;
    
    /* الظلال للثيم الداكن */
    --shadow-light: 8px 8px 16px #0f1114, -8px -8px 16px #353a42;
    --shadow-inset: inset 8px 8px 16px #0f1114, inset -8px -8px 16px #353a42;
    --shadow-hover: 4px 4px 8px #0f1114, -4px -4px 8px #353a42;
    --shadow-pressed: inset 4px 4px 8px #0f1114, inset -8px -8px 16px #353a42;
}

/* تطبيق الثيم الداكن على العناصر */
.dark-theme body {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.dark-theme .card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
}

.dark-theme .top-bar {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.dark-theme .sidebar {
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
}

.dark-theme .sidebar-header {
    border-bottom: 1px solid var(--border-color);
}

.dark-theme .nav-link {
    color: var(--text-secondary);
}

.dark-theme .nav-link:hover {
    background: rgba(115, 12, 2, 0.1);
    color: var(--secondary-color);
}

.dark-theme .nav-item.active .nav-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.dark-theme .form-control {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.dark-theme .form-control:focus {
    border-color: var(--primary-color);
    background: var(--bg-card);
}

.dark-theme .table {
    background: var(--bg-card);
    color: var(--text-primary);
}

.dark-theme .table th {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.dark-theme .table td {
    border-bottom: 1px solid var(--border-color);
}

.dark-theme .table tbody tr:hover {
    background: rgba(115, 12, 2, 0.05);
}

.dark-theme .modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.dark-theme .modal-header {
    border-bottom: 1px solid var(--border-color);
}

.dark-theme .modal-footer {
    border-top: 1px solid var(--border-color);
}

.dark-theme .user-dropdown {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
}

.dark-theme .user-dropdown a {
    color: var(--text-secondary);
}

.dark-theme .user-dropdown a:hover {
    background: rgba(115, 12, 2, 0.1);
    color: var(--secondary-color);
}

.dark-theme .login-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.dark-theme .login-footer {
    border-top: 1px solid var(--border-color);
}

.dark-theme .alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border-color: var(--success-color);
    color: #4caf50;
}

.dark-theme .alert-warning {
    background-color: rgba(255, 193, 7, 0.2);
    border-color: var(--warning-color);
    color: #ffeb3b;
}

.dark-theme .alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: var(--danger-color);
    color: #f44336;
}

.dark-theme .alert-info {
    background-color: rgba(15, 141, 191, 0.2);
    border-color: var(--info-color);
    color: #2196f3;
}

/* تأثيرات الانتقال بين الثيمات */
body,
.card,
.top-bar,
.sidebar,
.form-control,
.table,
.modal-content,
.user-dropdown,
.login-container {
    transition: background-color var(--transition-normal), 
                color var(--transition-normal), 
                border-color var(--transition-normal);
}

/* تخصيص شريط التمرير للثيم الداكن */
.dark-theme ::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

.dark-theme ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* تحسينات إضافية للثيم الداكن */
.dark-theme .sidebar-toggle:hover,
.dark-theme .theme-toggle button:hover,
.dark-theme .notification-btn:hover,
.dark-theme .user-btn:hover {
    background: rgba(115, 12, 2, 0.1);
    color: var(--secondary-color);
}

.dark-theme .close-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.dark-theme .toggle-password:hover {
    background: rgba(115, 12, 2, 0.1);
    color: var(--secondary-color);
}

/* تأثيرات خاصة للثيم الداكن */
.dark-theme .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover), 0 10px 20px rgba(115, 12, 2, 0.1);
}

.dark-theme .btn:hover {
    box-shadow: var(--shadow-hover), 0 5px 15px rgba(115, 12, 2, 0.2);
}

/* تخصيص الألوان للعناصر التفاعلية في الثيم الداكن */
.dark-theme .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: var(--shadow-light), 0 5px 15px rgba(115, 12, 2, 0.3);
}

.dark-theme .btn-primary:hover {
    box-shadow: var(--shadow-hover), 0 8px 20px rgba(115, 12, 2, 0.4);
}

.dark-theme .btn-secondary {
    background: var(--gray-600);
    color: var(--gray-900);
}

.dark-theme .btn-success {
    background: var(--success-color);
    box-shadow: var(--shadow-light), 0 5px 15px rgba(40, 167, 69, 0.3);
}

.dark-theme .btn-warning {
    background: var(--warning-color);
    color: var(--gray-900);
    box-shadow: var(--shadow-light), 0 5px 15px rgba(255, 193, 7, 0.3);
}

.dark-theme .btn-danger {
    background: var(--danger-color);
    box-shadow: var(--shadow-light), 0 5px 15px rgba(220, 53, 69, 0.3);
}

.dark-theme .btn-info {
    background: var(--info-color);
    box-shadow: var(--shadow-light), 0 5px 15px rgba(15, 141, 191, 0.3);
}

/* تحسين مظهر النماذج في الثيم الداكن */
.dark-theme .form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.dark-theme .form-control:focus::placeholder {
    opacity: 0.5;
}

/* تحسين مظهر الجداول في الثيم الداكن */
.dark-theme .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.02);
}

.dark-theme .table-hover tbody tr:hover {
    background-color: rgba(115, 12, 2, 0.08);
}

/* تأثيرات الإضاءة للثيم الداكن */
.dark-theme .nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px 0 0 2px;
}

/* تحسينات الاستجابة للثيم الداكن */
@media (max-width: 768px) {
    .dark-theme .sidebar {
        background: var(--bg-secondary);
        border-right: 1px solid var(--border-color);
    }
}

/* تأثيرات التركيز للثيم الداكن */
.dark-theme .btn:focus,
.dark-theme .form-control:focus {
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(115, 12, 2, 0.2);
}

.dark-theme .nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين مظهر الإشعارات في الثيم الداكن */
.dark-theme .notification-count {
    background: var(--danger-color);
    color: white;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* تأثيرات خاصة للعناصر النشطة في الثيم الداكن */
.dark-theme .active,
.dark-theme .selected {
    background: rgba(115, 12, 2, 0.1) !important;
    color: var(--secondary-color) !important;
    border-color: var(--primary-color) !important;
}
