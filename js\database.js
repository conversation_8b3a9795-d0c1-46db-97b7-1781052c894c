/**
 * Cybernet Magasin - إدارة قاعدة البيانات المحلية
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class Database {
    constructor() {
        this.dbName = 'cybernet_magasin';
        this.version = '1.0.0';
        this.tables = {
            settings: 'cm_settings',
            products: 'cm_products',
            categories: 'cm_categories',
            customers: 'cm_customers',
            suppliers: 'cm_suppliers',
            sales: 'cm_sales',
            purchases: 'cm_purchases',
            payments: 'cm_payments',
            inventory: 'cm_inventory'
        };
        this.init();
    }

    // تهيئة قاعدة البيانات
    init() {
        this.createDefaultData();
        this.migrateData();
    }

    // إنشاء البيانات الافتراضية
    createDefaultData() {
        // الإعدادات الافتراضية
        if (!this.exists(this.tables.settings)) {
            const defaultSettings = {
                company: {
                    name: 'Cybernet Magasin',
                    address: 'العنوان',
                    phone: '0123456789',
                    email: '<EMAIL>',
                    logo: '',
                    taxNumber: '',
                    currency: 'DZD'
                },
                system: {
                    password: Utils.simpleEncrypt('1234'),
                    taxRate: 19,
                    lowStockAlert: 10,
                    theme: 'light',
                    language: 'ar',
                    autoBackup: true,
                    backupInterval: 7
                },
                print: {
                    invoiceTemplate: 'default',
                    receiptTemplate: 'thermal',
                    showLogo: true,
                    showCompanyInfo: true,
                    showTax: true
                }
            };
            this.save(this.tables.settings, defaultSettings);
        }

        // الفئات الافتراضية
        if (!this.exists(this.tables.categories)) {
            const defaultCategories = [
                { id: 1, name: 'الخبز ومنتجاته', description: 'جميع أنواع الخبز والمعجنات', active: true, createdAt: new Date().toISOString() },
                { id: 2, name: 'السكر والمُحليات', description: 'السكر والعسل والمحليات الطبيعية', active: true, createdAt: new Date().toISOString() },
                { id: 3, name: 'منتجات الألبان', description: 'الحليب والجبن والزبدة', active: true, createdAt: new Date().toISOString() },
                { id: 4, name: 'المشروبات', description: 'العصائر والمياه والمشروبات الغازية', active: true, createdAt: new Date().toISOString() },
                { id: 5, name: 'المواد الغذائية', description: 'الأرز والمعكرونة والبقوليات', active: true, createdAt: new Date().toISOString() }
            ];
            this.save(this.tables.categories, defaultCategories);
        }

        // العميل الافتراضي
        if (!this.exists(this.tables.customers)) {
            const defaultCustomers = [
                {
                    id: 1,
                    name: 'ضيف',
                    phone: '',
                    email: '',
                    address: '',
                    balance: 0,
                    creditLimit: 0,
                    isDefault: true,
                    active: true,
                    createdAt: new Date().toISOString()
                }
            ];
            this.save(this.tables.customers, defaultCustomers);
        }

        // جداول فارغة أخرى
        const emptyTables = ['products', 'suppliers', 'sales', 'purchases', 'payments', 'inventory'];
        emptyTables.forEach(table => {
            if (!this.exists(this.tables[table])) {
                this.save(this.tables[table], []);
            }
        });
    }

    // ترحيل البيانات للإصدارات الجديدة
    migrateData() {
        const currentVersion = this.get('cm_version', '0.0.0');
        if (currentVersion !== this.version) {
            console.log(`ترحيل البيانات من الإصدار ${currentVersion} إلى ${this.version}`);
            // إضافة منطق الترحيل هنا حسب الحاجة
            this.save('cm_version', this.version);
        }
    }

    // حفظ البيانات
    save(key, data) {
        return StorageUtils.set(key, data);
    }

    // استرجاع البيانات
    get(key, defaultValue = null) {
        return StorageUtils.get(key, defaultValue);
    }

    // حذف البيانات
    remove(key) {
        return StorageUtils.remove(key);
    }

    // التحقق من وجود البيانات
    exists(key) {
        return StorageUtils.exists(key);
    }

    // مسح جميع البيانات
    clear() {
        return StorageUtils.clear();
    }

    // الحصول على جميع المنتجات
    getProducts() {
        return this.get(this.tables.products, []);
    }

    // حفظ المنتجات
    saveProducts(products) {
        return this.save(this.tables.products, products);
    }

    // إضافة منتج جديد
    addProduct(product) {
        const products = this.getProducts();
        product.id = this.generateId();
        product.createdAt = new Date().toISOString();
        product.updatedAt = new Date().toISOString();
        products.push(product);
        return this.saveProducts(products);
    }

    // تحديث منتج
    updateProduct(id, updatedProduct) {
        const products = this.getProducts();
        const index = products.findIndex(p => p.id === id);
        if (index !== -1) {
            products[index] = { ...products[index], ...updatedProduct, updatedAt: new Date().toISOString() };
            return this.saveProducts(products);
        }
        return false;
    }

    // حذف منتج
    deleteProduct(id) {
        const products = this.getProducts();
        const filteredProducts = products.filter(p => p.id !== id);
        return this.saveProducts(filteredProducts);
    }

    // البحث في المنتجات
    searchProducts(query) {
        const products = this.getProducts();
        const searchTerm = query.toLowerCase();
        return products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.barcode.includes(searchTerm)
        );
    }

    // الحصول على جميع العملاء
    getCustomers() {
        return this.get(this.tables.customers, []);
    }

    // حفظ العملاء
    saveCustomers(customers) {
        return this.save(this.tables.customers, customers);
    }

    // إضافة عميل جديد
    addCustomer(customer) {
        const customers = this.getCustomers();
        customer.id = this.generateId();
        customer.createdAt = new Date().toISOString();
        customer.updatedAt = new Date().toISOString();
        customer.balance = customer.balance || 0;
        customers.push(customer);
        return this.saveCustomers(customers);
    }

    // تحديث عميل
    updateCustomer(id, updatedCustomer) {
        const customers = this.getCustomers();
        const index = customers.findIndex(c => c.id === id);
        if (index !== -1) {
            customers[index] = { ...customers[index], ...updatedCustomer, updatedAt: new Date().toISOString() };
            return this.saveCustomers(customers);
        }
        return false;
    }

    // حذف عميل
    deleteCustomer(id) {
        const customers = this.getCustomers();
        const customer = customers.find(c => c.id === id);
        if (customer && customer.isDefault) {
            return false; // لا يمكن حذف العميل الافتراضي
        }
        const filteredCustomers = customers.filter(c => c.id !== id);
        return this.saveCustomers(filteredCustomers);
    }

    // الحصول على جميع الموردين
    getSuppliers() {
        return this.get(this.tables.suppliers, []);
    }

    // حفظ الموردين
    saveSuppliers(suppliers) {
        return this.save(this.tables.suppliers, suppliers);
    }

    // إضافة مورد جديد
    addSupplier(supplier) {
        const suppliers = this.getSuppliers();
        supplier.id = this.generateId();
        supplier.createdAt = new Date().toISOString();
        supplier.updatedAt = new Date().toISOString();
        supplier.balance = supplier.balance || 0;
        suppliers.push(supplier);
        return this.saveSuppliers(suppliers);
    }

    // تحديث مورد
    updateSupplier(id, updatedSupplier) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(s => s.id === id);
        if (index !== -1) {
            suppliers[index] = { ...suppliers[index], ...updatedSupplier, updatedAt: new Date().toISOString() };
            return this.saveSuppliers(suppliers);
        }
        return false;
    }

    // حذف مورد
    deleteSupplier(id) {
        const suppliers = this.getSuppliers();
        const filteredSuppliers = suppliers.filter(s => s.id !== id);
        return this.saveSuppliers(filteredSuppliers);
    }

    // الحصول على جميع المبيعات
    getSales() {
        return this.get(this.tables.sales, []);
    }

    // حفظ المبيعات
    saveSales(sales) {
        return this.save(this.tables.sales, sales);
    }

    // إضافة عملية بيع جديدة
    addSale(sale) {
        const sales = this.getSales();
        sale.id = this.generateId();
        sale.saleNumber = this.generateSaleNumber();
        sale.createdAt = new Date().toISOString();
        sales.push(sale);
        
        // تحديث المخزون
        this.updateInventoryAfterSale(sale.items);
        
        // تحديث رصيد العميل
        if (sale.paymentMethod === 'credit') {
            this.updateCustomerBalance(sale.customerId, sale.total);
        }
        
        return this.saveSales(sales);
    }

    // الحصول على جميع المشتريات
    getPurchases() {
        return this.get(this.tables.purchases, []);
    }

    // حفظ المشتريات
    savePurchases(purchases) {
        return this.save(this.tables.purchases, purchases);
    }

    // إضافة عملية شراء جديدة
    addPurchase(purchase) {
        const purchases = this.getPurchases();
        purchase.id = this.generateId();
        purchase.purchaseNumber = this.generatePurchaseNumber();
        purchase.createdAt = new Date().toISOString();
        purchases.push(purchase);
        
        // تحديث المخزون
        this.updateInventoryAfterPurchase(purchase.items);
        
        return this.savePurchases(purchases);
    }

    // الحصول على الفئات
    getCategories() {
        return this.get(this.tables.categories, []);
    }

    // حفظ الفئات
    saveCategories(categories) {
        return this.save(this.tables.categories, categories);
    }

    // الحصول على الإعدادات
    getSettings() {
        return this.get(this.tables.settings, {});
    }

    // حفظ الإعدادات
    saveSettings(settings) {
        return this.save(this.tables.settings, settings);
    }

    // تحديث المخزون بعد البيع
    updateInventoryAfterSale(items) {
        const products = this.getProducts();
        items.forEach(item => {
            const productIndex = products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                products[productIndex].quantity -= item.quantity;
                products[productIndex].updatedAt = new Date().toISOString();
            }
        });
        this.saveProducts(products);
    }

    // تحديث المخزون بعد الشراء
    updateInventoryAfterPurchase(items) {
        const products = this.getProducts();
        items.forEach(item => {
            const productIndex = products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                products[productIndex].quantity += item.quantity;
                products[productIndex].updatedAt = new Date().toISOString();
            }
        });
        this.saveProducts(products);
    }

    // تحديث رصيد العميل
    updateCustomerBalance(customerId, amount) {
        const customers = this.getCustomers();
        const customerIndex = customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            customers[customerIndex].balance += amount;
            customers[customerIndex].updatedAt = new Date().toISOString();
            this.saveCustomers(customers);
        }
    }

    // توليد معرف فريد
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    // توليد رقم فاتورة بيع
    generateSaleNumber() {
        const sales = this.getSales();
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const todayStr = `${year}${month}${day}`;
        
        const todaySales = sales.filter(sale => 
            sale.saleNumber && sale.saleNumber.startsWith(`S${todayStr}`)
        );
        
        const nextNumber = todaySales.length + 1;
        return `S${todayStr}${String(nextNumber).padStart(3, '0')}`;
    }

    // توليد رقم فاتورة شراء
    generatePurchaseNumber() {
        const purchases = this.getPurchases();
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const todayStr = `${year}${month}${day}`;
        
        const todayPurchases = purchases.filter(purchase => 
            purchase.purchaseNumber && purchase.purchaseNumber.startsWith(`P${todayStr}`)
        );
        
        const nextNumber = todayPurchases.length + 1;
        return `P${todayStr}${String(nextNumber).padStart(3, '0')}`;
    }

    // تصدير البيانات
    exportData() {
        const data = {};
        Object.values(this.tables).forEach(table => {
            data[table] = this.get(table, []);
        });
        data.settings = this.getSettings();
        data.version = this.version;
        data.exportDate = new Date().toISOString();
        return JSON.stringify(data, null, 2);
    }

    // استيراد البيانات
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            Object.keys(data).forEach(key => {
                if (key !== 'exportDate' && key !== 'version') {
                    this.save(key, data[key]);
                }
            });
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // إحصائيات سريعة
    getQuickStats() {
        const products = this.getProducts();
        const customers = this.getCustomers();
        const sales = this.getSales();
        const today = new Date().toISOString().split('T')[0];
        
        const todaySales = sales.filter(sale => 
            sale.createdAt.split('T')[0] === today
        );
        
        const lowStockProducts = products.filter(product => 
            product.quantity <= (this.getSettings().system?.lowStockAlert || 10)
        );
        
        return {
            totalProducts: products.length,
            totalCustomers: customers.length - 1, // استثناء العميل الافتراضي
            totalSales: sales.length,
            todaySales: todaySales.length,
            todayRevenue: todaySales.reduce((sum, sale) => sum + sale.total, 0),
            lowStockCount: lowStockProducts.length,
            totalRevenue: sales.reduce((sum, sale) => sum + sale.total, 0)
        };
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const db = new Database();

// تصدير قاعدة البيانات للاستخدام العام
window.db = db;
