/**
 * Cybernet Magasin - نظام المشتريات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class PurchasesManager {
    constructor() {
        this.purchases = [];
        this.suppliers = [];
        this.products = [];
        this.currentPurchase = {
            items: [],
            supplier: null,
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            paymentMethod: 'cash'
        };
        this.filteredPurchases = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.init();
    }

    // تهيئة مدير المشتريات
    init() {
        this.loadData();
        this.resetPurchase();
    }

    // تحميل البيانات
    loadData() {
        this.purchases = db.getPurchases();
        this.suppliers = db.getSuppliers();
        this.products = db.getProducts();
        this.filteredPurchases = [...this.purchases];
    }

    // إعادة تعيين عملية الشراء
    resetPurchase() {
        this.currentPurchase = {
            items: [],
            supplier: null,
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            paymentMethod: 'cash',
            notes: ''
        };
        this.calculateTotals();
    }

    // عرض واجهة المشتريات
    render() {
        const container = DOMUtils.$('#purchases-page');
        if (!container) return;

        this.loadData();

        container.innerHTML = `
            <div class="purchases-container">
                <!-- شريط الأدوات -->
                <div class="purchases-toolbar">
                    <div class="toolbar-left">
                        <h2>إدارة المشتريات</h2>
                        <div class="toolbar-tabs">
                            <button class="tab-btn active" onclick="purchasesManager.showTab('new-purchase')">
                                <i class="fas fa-plus"></i>
                                شراء جديد
                            </button>
                            <button class="tab-btn" onclick="purchasesManager.showTab('purchases-list')">
                                <i class="fas fa-list"></i>
                                قائمة المشتريات
                            </button>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-info" onclick="purchasesManager.exportPurchases()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="btn btn-warning" onclick="purchasesManager.showPurchasesReport()">
                            <i class="fas fa-chart-bar"></i>
                            تقرير المشتريات
                        </button>
                    </div>
                </div>

                <!-- تبويب الشراء الجديد -->
                <div id="new-purchase-tab" class="tab-content active">
                    <div class="purchase-form-container">
                        <div class="purchase-header">
                            <div class="supplier-selection">
                                <label>المورد *</label>
                                <select id="purchase-supplier" onchange="purchasesManager.selectSupplier()" required>
                                    <option value="">اختر المورد</option>
                                    ${this.renderSupplierOptions()}
                                </select>
                                <button class="btn btn-sm btn-primary" onclick="purchasesManager.showAddSupplierModal()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="purchase-info">
                                <div class="form-group">
                                    <label>تاريخ الشراء</label>
                                    <input type="date" id="purchase-date" value="${DateUtils.getCurrentDate()}">
                                </div>
                                <div class="form-group">
                                    <label>رقم الفاتورة</label>
                                    <input type="text" id="purchase-invoice-number" placeholder="رقم فاتورة المورد">
                                </div>
                            </div>
                        </div>

                        <!-- إضافة المنتجات -->
                        <div class="add-product-section">
                            <h4>إضافة منتجات</h4>
                            <div class="product-search">
                                <div class="search-box">
                                    <input type="text" id="product-search" placeholder="ابحث عن منتج...">
                                    <button onclick="purchasesManager.searchProducts()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <button class="btn btn-secondary" onclick="purchasesManager.showProductsList()">
                                    <i class="fas fa-list"></i>
                                    قائمة المنتجات
                                </button>
                                <button class="btn btn-success" onclick="purchasesManager.showAddProductModal()">
                                    <i class="fas fa-plus"></i>
                                    منتج جديد
                                </button>
                            </div>
                            
                            <div class="quick-add-product">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>المنتج</label>
                                        <select id="quick-product-select">
                                            <option value="">اختر منتج</option>
                                            ${this.renderProductOptions()}
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>الكمية</label>
                                        <input type="number" id="quick-quantity" min="1" value="1">
                                    </div>
                                    <div class="form-group">
                                        <label>سعر الشراء</label>
                                        <input type="number" id="quick-purchase-price" step="0.01" min="0">
                                    </div>
                                    <div class="form-group">
                                        <button class="btn btn-primary" onclick="purchasesManager.addProductToPurchase()">
                                            <i class="fas fa-plus"></i>
                                            إضافة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة المنتجات المضافة -->
                        <div class="purchase-items">
                            <h4>المنتجات المضافة</h4>
                            <div class="items-table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الشراء</th>
                                            <th>الإجمالي</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="purchase-items-body">
                                        ${this.renderPurchaseItems()}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- الإجماليات -->
                        <div class="purchase-totals">
                            <div class="totals-grid">
                                <div class="total-row">
                                    <span>المجموع الفرعي:</span>
                                    <span id="purchase-subtotal">${NumberUtils.formatCurrency(this.currentPurchase.subtotal)}</span>
                                </div>
                                <div class="total-row">
                                    <span>الخصم:</span>
                                    <div class="discount-input">
                                        <input type="number" id="purchase-discount" value="${this.currentPurchase.discount}" 
                                               onchange="purchasesManager.applyDiscount()" min="0" step="0.01">
                                        <span>دج</span>
                                    </div>
                                </div>
                                <div class="total-row">
                                    <span>الضريبة (19%):</span>
                                    <span id="purchase-tax">${NumberUtils.formatCurrency(this.currentPurchase.tax)}</span>
                                </div>
                                <div class="total-row grand-total">
                                    <span>الإجمالي:</span>
                                    <span id="purchase-total">${NumberUtils.formatCurrency(this.currentPurchase.total)}</span>
                                </div>
                            </div>
                        </div>

                        <!-- طريقة الدفع والملاحظات -->
                        <div class="purchase-details">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>طريقة الدفع</label>
                                    <select id="purchase-payment-method" onchange="purchasesManager.selectPaymentMethod()">
                                        <option value="cash">نقداً</option>
                                        <option value="credit">على الحساب</option>
                                        <option value="bank">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>حالة الاستلام</label>
                                    <select id="purchase-status">
                                        <option value="received">مستلم</option>
                                        <option value="pending">في الانتظار</option>
                                        <option value="partial">استلام جزئي</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="purchase-notes" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <!-- أزرار العمليات -->
                        <div class="purchase-actions">
                            <button class="btn btn-success btn-lg" onclick="purchasesManager.savePurchase()" 
                                    ${this.currentPurchase.items.length === 0 ? 'disabled' : ''}>
                                <i class="fas fa-save"></i>
                                حفظ الشراء
                            </button>
                            <button class="btn btn-secondary" onclick="purchasesManager.resetPurchase(); purchasesManager.updatePurchaseForm();">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-info" onclick="purchasesManager.previewPurchase()">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويب قائمة المشتريات -->
                <div id="purchases-list-tab" class="tab-content">
                    <!-- أدوات البحث والفلترة -->
                    <div class="purchases-filters">
                        <div class="filter-group">
                            <label>البحث:</label>
                            <div class="search-box">
                                <input type="text" id="purchases-search" placeholder="ابحث برقم الفاتورة أو المورد...">
                                <button onclick="purchasesManager.searchPurchases()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label>المورد:</label>
                            <select id="supplier-filter" onchange="purchasesManager.filterBySupplier()">
                                <option value="">جميع الموردين</option>
                                ${this.renderSupplierOptions()}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="status-filter" onchange="purchasesManager.filterByStatus()">
                                <option value="">الكل</option>
                                <option value="received">مستلم</option>
                                <option value="pending">في الانتظار</option>
                                <option value="partial">استلام جزئي</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ:</label>
                            <input type="date" id="date-from" onchange="purchasesManager.filterByDate()">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" id="date-to" onchange="purchasesManager.filterByDate()">
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-sm btn-secondary" onclick="purchasesManager.clearFilters()">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>

                    <!-- جدول المشتريات -->
                    <div class="purchases-table-container">
                        <table class="table purchases-table">
                            <thead>
                                <tr>
                                    <th>رقم الشراء</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>الإجمالي</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="purchases-table-body">
                                ${this.renderPurchasesTable()}
                            </tbody>
                        </table>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    <div class="pagination-container">
                        ${this.renderPagination()}
                    </div>
                </div>
            </div>

            <!-- نافذة قائمة المنتجات -->
            <div id="products-list-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>اختيار منتج</h3>
                        <button class="close-btn" onclick="closeModal('products-list-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="products-table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>السعر الحالي</th>
                                        <th>الكمية المتاحة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="products-list-body">
                                    ${this.renderProductsListTable()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة عرض تفاصيل الشراء -->
            <div id="purchase-details-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل الشراء</h3>
                        <button class="close-btn" onclick="closeModal('purchase-details-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body" id="purchase-details-content">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // البحث في المنتجات
        const productSearchInput = DOMUtils.$('#product-search');
        if (productSearchInput) {
            productSearchInput.addEventListener('input', (e) => {
                this.searchProducts(e.target.value);
            });
        }

        // البحث في المشتريات
        const purchasesSearchInput = DOMUtils.$('#purchases-search');
        if (purchasesSearchInput) {
            purchasesSearchInput.addEventListener('input', (e) => {
                this.searchPurchases(e.target.value);
            });
        }

        // تحديث سعر الشراء عند اختيار منتج
        const quickProductSelect = DOMUtils.$('#quick-product-select');
        if (quickProductSelect) {
            quickProductSelect.addEventListener('change', (e) => {
                this.updatePurchasePrice(e.target.value);
            });
        }
    }

    // عرض التبويبات
    showTab(tabName) {
        // إخفاء جميع التبويبات
        const tabs = DOMUtils.$$('.tab-content');
        tabs.forEach(tab => {
            DOMUtils.removeClass(tab, 'active');
        });

        // إزالة الفئة النشطة من جميع الأزرار
        const tabBtns = DOMUtils.$$('.tab-btn');
        tabBtns.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
        });

        // إظهار التبويب المطلوب
        const targetTab = DOMUtils.$(`#${tabName}-tab`);
        if (targetTab) {
            DOMUtils.addClass(targetTab, 'active');
        }

        // تفعيل الزر المناسب
        const targetBtn = DOMUtils.$(`[onclick="purchasesManager.showTab('${tabName}')"]`);
        if (targetBtn) {
            DOMUtils.addClass(targetBtn, 'active');
        }

        // تحديث البيانات حسب التبويب
        if (tabName === 'purchases-list') {
            this.loadData();
            this.updatePurchasesTable();
        }
    }

    // عرض خيارات الموردين
    renderSupplierOptions() {
        return this.suppliers.filter(s => s.active).map(supplier =>
            `<option value="${supplier.id}">${supplier.name}</option>`
        ).join('');
    }

    // عرض خيارات المنتجات
    renderProductOptions() {
        return this.products.filter(p => p.active).map(product =>
            `<option value="${product.id}">${product.name}</option>`
        ).join('');
    }

    // عرض عناصر الشراء
    renderPurchaseItems() {
        if (this.currentPurchase.items.length === 0) {
            return `
                <tr>
                    <td colspan="5" class="text-center">
                        <div class="no-data">
                            <i class="fas fa-shopping-cart"></i>
                            <p>لم يتم إضافة منتجات بعد</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return this.currentPurchase.items.map((item, index) => `
            <tr>
                <td>${item.name}</td>
                <td>
                    <input type="number" value="${item.quantity}" min="1"
                           onchange="purchasesManager.updateItemQuantity(${index}, this.value)"
                           class="quantity-input">
                </td>
                <td>
                    <input type="number" value="${item.purchasePrice}" min="0" step="0.01"
                           onchange="purchasesManager.updateItemPrice(${index}, this.value)"
                           class="price-input">
                </td>
                <td class="total-cell">${NumberUtils.formatCurrency(item.total)}</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="purchasesManager.removeItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // عرض جدول قائمة المنتجات
    renderProductsListTable() {
        return this.products.filter(p => p.active).map(product => `
            <tr>
                <td>${product.name}</td>
                <td>${NumberUtils.formatCurrency(product.price)}</td>
                <td>${product.quantity}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="purchasesManager.selectProductFromList('${product.id}')">
                        اختيار
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // عرض جدول المشتريات
    renderPurchasesTable() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pagePurchases = this.filteredPurchases.slice(startIndex, endIndex);

        if (pagePurchases.length === 0) {
            return `
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="no-data">
                            <i class="fas fa-shopping-cart"></i>
                            <p>لا توجد مشتريات</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return pagePurchases.map(purchase => {
            const supplier = this.suppliers.find(s => s.id === purchase.supplierId);

            return `
                <tr>
                    <td>${purchase.purchaseNumber}</td>
                    <td>${supplier ? supplier.name : 'غير محدد'}</td>
                    <td>${DateUtils.formatDate(purchase.createdAt)}</td>
                    <td class="amount-cell">${NumberUtils.formatCurrency(purchase.total)}</td>
                    <td>${this.getPaymentMethodText(purchase.paymentMethod)}</td>
                    <td>
                        <span class="status-badge ${purchase.status}">
                            ${this.getStatusText(purchase.status)}
                        </span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="purchasesManager.viewPurchase('${purchase.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="purchasesManager.printPurchase('${purchase.id}')" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="purchasesManager.editPurchaseStatus('${purchase.id}')" title="تعديل الحالة">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // عرض التنقل بين الصفحات
    renderPagination() {
        const totalPages = Math.ceil(this.filteredPurchases.length / this.itemsPerPage);

        if (totalPages <= 1) return '';

        let pagination = '<div class="pagination">';

        if (this.currentPage > 1) {
            pagination += `<button class="page-btn" onclick="purchasesManager.goToPage(${this.currentPage - 1})">السابق</button>`;
        }

        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                pagination += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination += `<button class="page-btn" onclick="purchasesManager.goToPage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination += '<span class="page-dots">...</span>';
            }
        }

        if (this.currentPage < totalPages) {
            pagination += `<button class="page-btn" onclick="purchasesManager.goToPage(${this.currentPage + 1})">التالي</button>`;
        }

        pagination += '</div>';
        return pagination;
    }

    // اختيار المورد
    selectSupplier() {
        const supplierSelect = DOMUtils.$('#purchase-supplier');
        const supplierId = supplierSelect?.value;
        this.currentPurchase.supplier = this.suppliers.find(s => s.id === supplierId);
    }

    // تحديث سعر الشراء
    updatePurchasePrice(productId) {
        if (!productId) return;

        const product = this.products.find(p => p.id === productId);
        if (product) {
            DOMUtils.$('#quick-purchase-price').value = product.price;
        }
    }

    // إضافة منتج للشراء
    addProductToPurchase() {
        const productSelect = DOMUtils.$('#quick-product-select');
        const quantityInput = DOMUtils.$('#quick-quantity');
        const priceInput = DOMUtils.$('#quick-purchase-price');

        const productId = productSelect?.value;
        const quantity = parseInt(quantityInput?.value || 0);
        const purchasePrice = parseFloat(priceInput?.value || 0);

        if (!productId) {
            app.showNotification('يرجى اختيار منتج', 'warning');
            return;
        }

        if (quantity <= 0) {
            app.showNotification('الكمية يجب أن تكون أكبر من صفر', 'warning');
            return;
        }

        if (purchasePrice <= 0) {
            app.showNotification('سعر الشراء يجب أن يكون أكبر من صفر', 'warning');
            return;
        }

        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // البحث عن المنتج في القائمة
        const existingItemIndex = this.currentPurchase.items.findIndex(item => item.productId === productId);

        if (existingItemIndex !== -1) {
            // تحديث الكمية والسعر
            this.currentPurchase.items[existingItemIndex].quantity += quantity;
            this.currentPurchase.items[existingItemIndex].purchasePrice = purchasePrice;
            this.currentPurchase.items[existingItemIndex].total =
                this.currentPurchase.items[existingItemIndex].quantity * purchasePrice;
        } else {
            // إضافة منتج جديد
            this.currentPurchase.items.push({
                productId: product.id,
                name: product.name,
                quantity: quantity,
                purchasePrice: purchasePrice,
                total: quantity * purchasePrice
            });
        }

        // إعادة تعيين النموذج
        productSelect.value = '';
        quantityInput.value = '1';
        priceInput.value = '';

        this.calculateTotals();
        this.updatePurchaseForm();
        app.showNotification(`تم إضافة ${product.name}`, 'success');
    }

    // تحديث كمية المنتج
    updateItemQuantity(index, newQuantity) {
        const quantity = parseInt(newQuantity);
        if (quantity <= 0) {
            this.removeItem(index);
            return;
        }

        this.currentPurchase.items[index].quantity = quantity;
        this.currentPurchase.items[index].total =
            quantity * this.currentPurchase.items[index].purchasePrice;

        this.calculateTotals();
        this.updatePurchaseForm();
    }

    // تحديث سعر المنتج
    updateItemPrice(index, newPrice) {
        const price = parseFloat(newPrice);
        if (price <= 0) {
            app.showNotification('السعر يجب أن يكون أكبر من صفر', 'warning');
            return;
        }

        this.currentPurchase.items[index].purchasePrice = price;
        this.currentPurchase.items[index].total =
            this.currentPurchase.items[index].quantity * price;

        this.calculateTotals();
        this.updatePurchaseForm();
    }

    // إزالة منتج
    removeItem(index) {
        this.currentPurchase.items.splice(index, 1);
        this.calculateTotals();
        this.updatePurchaseForm();
    }

    // حساب الإجماليات
    calculateTotals() {
        this.currentPurchase.subtotal = this.currentPurchase.items.reduce((sum, item) => sum + item.total, 0);

        const discountedSubtotal = this.currentPurchase.subtotal - this.currentPurchase.discount;
        this.currentPurchase.tax = (discountedSubtotal * 19) / 100; // ضريبة 19%
        this.currentPurchase.total = discountedSubtotal + this.currentPurchase.tax;
    }

    // تطبيق الخصم
    applyDiscount() {
        const discountInput = DOMUtils.$('#purchase-discount');
        const discount = parseFloat(discountInput?.value || 0);

        if (discount < 0) {
            app.showNotification('قيمة الخصم لا يمكن أن تكون سالبة', 'error');
            discountInput.value = 0;
            return;
        }

        if (discount > this.currentPurchase.subtotal) {
            app.showNotification('قيمة الخصم لا يمكن أن تتجاوز المجموع الفرعي', 'error');
            discountInput.value = this.currentPurchase.subtotal;
            this.currentPurchase.discount = this.currentPurchase.subtotal;
        } else {
            this.currentPurchase.discount = discount;
        }

        this.calculateTotals();
        this.updateTotalsDisplay();
    }

    // اختيار طريقة الدفع
    selectPaymentMethod() {
        const paymentMethodSelect = DOMUtils.$('#purchase-payment-method');
        this.currentPurchase.paymentMethod = paymentMethodSelect?.value || 'cash';
    }

    // تحديث نموذج الشراء
    updatePurchaseForm() {
        const itemsBody = DOMUtils.$('#purchase-items-body');
        if (itemsBody) {
            itemsBody.innerHTML = this.renderPurchaseItems();
        }

        this.updateTotalsDisplay();
        this.updateActionButtons();
    }

    // تحديث عرض الإجماليات
    updateTotalsDisplay() {
        const subtotalElement = DOMUtils.$('#purchase-subtotal');
        const taxElement = DOMUtils.$('#purchase-tax');
        const totalElement = DOMUtils.$('#purchase-total');

        if (subtotalElement) subtotalElement.textContent = NumberUtils.formatCurrency(this.currentPurchase.subtotal);
        if (taxElement) taxElement.textContent = NumberUtils.formatCurrency(this.currentPurchase.tax);
        if (totalElement) totalElement.textContent = NumberUtils.formatCurrency(this.currentPurchase.total);
    }

    // تحديث أزرار العمليات
    updateActionButtons() {
        const saveBtn = DOMUtils.$('.purchase-actions .btn-success');
        if (saveBtn) {
            saveBtn.disabled = this.currentPurchase.items.length === 0;
        }
    }

    // إظهار قائمة المنتجات
    showProductsList() {
        const modal = DOMUtils.$('#products-list-modal');
        const tableBody = DOMUtils.$('#products-list-body');

        if (tableBody) {
            tableBody.innerHTML = this.renderProductsListTable();
        }

        DOMUtils.addClass(modal, 'show');
    }

    // اختيار منتج من القائمة
    selectProductFromList(productId) {
        const productSelect = DOMUtils.$('#quick-product-select');
        if (productSelect) {
            productSelect.value = productId;
            this.updatePurchasePrice(productId);
        }

        app.closeModal('products-list-modal');
        DOMUtils.$('#quick-quantity')?.focus();
    }

    // البحث في المنتجات
    searchProducts(query = '') {
        // يمكن إضافة منطق البحث هنا
        console.log('البحث عن:', query);
    }

    // حفظ الشراء
    savePurchase() {
        if (this.currentPurchase.items.length === 0) {
            app.showNotification('يرجى إضافة منتجات للشراء', 'warning');
            return;
        }

        if (!this.currentPurchase.supplier) {
            app.showNotification('يرجى اختيار المورد', 'warning');
            return;
        }

        const purchaseDate = DOMUtils.$('#purchase-date')?.value;
        const invoiceNumber = DOMUtils.$('#purchase-invoice-number')?.value?.trim();
        const paymentMethod = DOMUtils.$('#purchase-payment-method')?.value;
        const status = DOMUtils.$('#purchase-status')?.value;
        const notes = DOMUtils.$('#purchase-notes')?.value?.trim();

        const purchase = {
            supplierId: this.currentPurchase.supplier.id,
            items: [...this.currentPurchase.items],
            subtotal: this.currentPurchase.subtotal,
            discount: this.currentPurchase.discount,
            tax: this.currentPurchase.tax,
            total: this.currentPurchase.total,
            paymentMethod: paymentMethod,
            status: status,
            purchaseDate: purchaseDate,
            invoiceNumber: invoiceNumber,
            notes: notes
        };

        if (db.addPurchase(purchase)) {
            app.showNotification('تم حفظ الشراء بنجاح', 'success');

            // تحديث رصيد المورد إذا كان الدفع على الحساب
            if (paymentMethod === 'credit') {
                const newBalance = this.currentPurchase.supplier.balance + this.currentPurchase.total;
                db.updateSupplier(this.currentPurchase.supplier.id, { balance: newBalance });
            }

            this.resetPurchase();
            this.updatePurchaseForm();

            // تحديث لوحة المعلومات
            if (window.dashboard) {
                window.dashboard.updateStats();
            }
        } else {
            app.showNotification('فشل في حفظ الشراء', 'error');
        }
    }

    // معاينة الشراء
    previewPurchase() {
        if (this.currentPurchase.items.length === 0) {
            app.showNotification('لا توجد منتجات للمعاينة', 'warning');
            return;
        }

        // يمكن إضافة منطق المعاينة هنا
        app.showNotification('معاينة الشراء قيد التطوير', 'info');
    }

    // البحث في المشتريات
    searchPurchases(query = '') {
        const searchInput = DOMUtils.$('#purchases-search');
        const searchQuery = query || searchInput?.value || '';

        if (searchQuery.trim() === '') {
            this.filteredPurchases = [...this.purchases];
        } else {
            const searchTerm = searchQuery.toLowerCase();
            this.filteredPurchases = this.purchases.filter(purchase => {
                const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
                return purchase.purchaseNumber.toLowerCase().includes(searchTerm) ||
                       (purchase.invoiceNumber && purchase.invoiceNumber.toLowerCase().includes(searchTerm)) ||
                       (supplier && supplier.name.toLowerCase().includes(searchTerm));
            });
        }

        this.currentPage = 1;
        this.updatePurchasesTable();
    }

    // فلترة حسب المورد
    filterBySupplier() {
        const supplierSelect = DOMUtils.$('#supplier-filter');
        const supplierId = supplierSelect?.value;

        if (!supplierId) {
            this.filteredPurchases = [...this.purchases];
        } else {
            this.filteredPurchases = this.purchases.filter(purchase =>
                purchase.supplierId === supplierId
            );
        }

        this.currentPage = 1;
        this.updatePurchasesTable();
    }

    // فلترة حسب الحالة
    filterByStatus() {
        const statusSelect = DOMUtils.$('#status-filter');
        const status = statusSelect?.value;

        if (!status) {
            this.filteredPurchases = [...this.purchases];
        } else {
            this.filteredPurchases = this.purchases.filter(purchase =>
                purchase.status === status
            );
        }

        this.currentPage = 1;
        this.updatePurchasesTable();
    }

    // فلترة حسب التاريخ
    filterByDate() {
        const dateFromInput = DOMUtils.$('#date-from');
        const dateToInput = DOMUtils.$('#date-to');

        const dateFrom = dateFromInput?.value;
        const dateTo = dateToInput?.value;

        if (!dateFrom && !dateTo) {
            this.filteredPurchases = [...this.purchases];
        } else {
            this.filteredPurchases = this.purchases.filter(purchase => {
                const purchaseDate = purchase.purchaseDate || purchase.createdAt.split('T')[0];

                if (dateFrom && dateTo) {
                    return purchaseDate >= dateFrom && purchaseDate <= dateTo;
                } else if (dateFrom) {
                    return purchaseDate >= dateFrom;
                } else if (dateTo) {
                    return purchaseDate <= dateTo;
                }

                return true;
            });
        }

        this.currentPage = 1;
        this.updatePurchasesTable();
    }

    // مسح الفلاتر
    clearFilters() {
        DOMUtils.$('#purchases-search').value = '';
        DOMUtils.$('#supplier-filter').value = '';
        DOMUtils.$('#status-filter').value = '';
        DOMUtils.$('#date-from').value = '';
        DOMUtils.$('#date-to').value = '';

        this.filteredPurchases = [...this.purchases];
        this.currentPage = 1;
        this.updatePurchasesTable();
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.updatePurchasesTable();
    }

    // تحديث جدول المشتريات
    updatePurchasesTable() {
        const tableBody = DOMUtils.$('#purchases-table-body');
        const paginationContainer = DOMUtils.$('.pagination-container');

        if (tableBody) {
            tableBody.innerHTML = this.renderPurchasesTable();
        }

        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }
    }

    // عرض تفاصيل الشراء
    viewPurchase(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
        const detailsContent = DOMUtils.$('#purchase-details-content');

        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="purchase-details">
                    <div class="purchase-header">
                        <div class="purchase-info">
                            <h3>فاتورة شراء #${purchase.purchaseNumber}</h3>
                            <div class="purchase-meta">
                                <p><strong>المورد:</strong> ${supplier ? supplier.name : 'غير محدد'}</p>
                                <p><strong>التاريخ:</strong> ${DateUtils.formatDate(purchase.purchaseDate || purchase.createdAt)}</p>
                                <p><strong>رقم فاتورة المورد:</strong> ${purchase.invoiceNumber || 'غير محدد'}</p>
                                <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(purchase.paymentMethod)}</p>
                                <p><strong>الحالة:</strong>
                                    <span class="status-badge ${purchase.status}">
                                        ${this.getStatusText(purchase.status)}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="purchase-actions">
                            <button class="btn btn-primary" onclick="purchasesManager.printPurchase('${purchase.id}')">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <div class="purchase-items-details">
                        <h4>المنتجات المشتراة</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>سعر الشراء</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchase.items.map(item => `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${NumberUtils.formatCurrency(item.purchasePrice)}</td>
                                        <td>${NumberUtils.formatCurrency(item.total)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="purchase-totals-details">
                        <div class="totals-summary">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span>${NumberUtils.formatCurrency(purchase.subtotal)}</span>
                            </div>
                            <div class="total-row">
                                <span>الخصم:</span>
                                <span>${NumberUtils.formatCurrency(purchase.discount)}</span>
                            </div>
                            <div class="total-row">
                                <span>الضريبة:</span>
                                <span>${NumberUtils.formatCurrency(purchase.tax)}</span>
                            </div>
                            <div class="total-row grand-total">
                                <span>الإجمالي:</span>
                                <span>${NumberUtils.formatCurrency(purchase.total)}</span>
                            </div>
                        </div>
                    </div>

                    ${purchase.notes ? `
                        <div class="purchase-notes">
                            <h4>ملاحظات</h4>
                            <p>${purchase.notes}</p>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        DOMUtils.addClass('#purchase-details-modal', 'show');
    }

    // طباعة الشراء
    printPurchase(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        // يمكن إضافة منطق الطباعة هنا
        app.showNotification('ميزة الطباعة قيد التطوير', 'info');
    }

    // تعديل حالة الشراء
    editPurchaseStatus(purchaseId) {
        const purchase = this.purchases.find(p => p.id === purchaseId);
        if (!purchase) return;

        const newStatus = prompt('أدخل الحالة الجديدة:\nreceived = مستلم\npending = في الانتظار\npartial = استلام جزئي', purchase.status);

        if (newStatus && ['received', 'pending', 'partial'].includes(newStatus)) {
            // تحديث حالة الشراء في قاعدة البيانات
            const purchases = db.getPurchases();
            const purchaseIndex = purchases.findIndex(p => p.id === purchaseId);

            if (purchaseIndex !== -1) {
                purchases[purchaseIndex].status = newStatus;
                purchases[purchaseIndex].updatedAt = new Date().toISOString();

                if (db.savePurchases(purchases)) {
                    app.showNotification('تم تحديث حالة الشراء', 'success');
                    this.loadData();
                    this.updatePurchasesTable();
                } else {
                    app.showNotification('فشل في تحديث الحالة', 'error');
                }
            }
        }
    }

    // إظهار نافذة إضافة مورد
    showAddSupplierModal() {
        if (window.suppliersManager) {
            window.suppliersManager.showAddSupplierModal();
        } else {
            app.showNotification('مدير الموردين غير متوفر', 'error');
        }
    }

    // إظهار نافذة إضافة منتج
    showAddProductModal() {
        if (window.productsManager) {
            window.productsManager.showAddProductModal();
        } else {
            app.showNotification('مدير المنتجات غير متوفر', 'error');
        }
    }

    // تصدير المشتريات
    exportPurchases() {
        const data = this.filteredPurchases.map(purchase => {
            const supplier = this.suppliers.find(s => s.id === purchase.supplierId);
            return {
                'رقم الشراء': purchase.purchaseNumber,
                'المورد': supplier ? supplier.name : 'غير محدد',
                'التاريخ': DateUtils.formatDate(purchase.purchaseDate || purchase.createdAt),
                'رقم فاتورة المورد': purchase.invoiceNumber || '',
                'المجموع الفرعي': purchase.subtotal,
                'الخصم': purchase.discount,
                'الضريبة': purchase.tax,
                'الإجمالي': purchase.total,
                'طريقة الدفع': this.getPaymentMethodText(purchase.paymentMethod),
                'الحالة': this.getStatusText(purchase.status),
                'ملاحظات': purchase.notes || ''
            };
        });

        const csvContent = this.convertToCSV(data);
        const filename = `purchases-${DateUtils.getCurrentDate()}.csv`;

        Utils.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
        app.showNotification('تم تصدير المشتريات بنجاح', 'success');
    }

    // إظهار تقرير المشتريات
    showPurchasesReport() {
        // يمكن إضافة منطق التقرير هنا
        app.showNotification('تقرير المشتريات قيد التطوير', 'info');
    }

    // الحصول على نص طريقة الدفع
    getPaymentMethodText(method) {
        const methods = {
            'cash': 'نقداً',
            'credit': 'على الحساب',
            'bank': 'تحويل بنكي',
            'check': 'شيك'
        };
        return methods[method] || method;
    }

    // الحصول على نص الحالة
    getStatusText(status) {
        const statuses = {
            'received': 'مستلم',
            'pending': 'في الانتظار',
            'partial': 'استلام جزئي'
        };
        return statuses[status] || status;
    }

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [];

        // إضافة العناوين
        csvRows.push(headers.join(','));

        // إضافة البيانات
        data.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }
}

// إنشاء مثيل من مدير المشتريات
const purchasesManager = new PurchasesManager();

// تصدير مدير المشتريات للاستخدام العام
window.purchasesManager = purchasesManager;
