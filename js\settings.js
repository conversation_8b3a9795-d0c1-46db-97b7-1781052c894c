/**
 * Cybernet Magasin - إعدادات النظام
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class SettingsManager {
    constructor() {
        this.settings = {};
        this.init();
    }

    // تهيئة مدير الإعدادات
    init() {
        this.loadSettings();
    }

    // تحميل الإعدادات
    loadSettings() {
        this.settings = db.getSettings();
    }

    // عرض واجهة الإعدادات
    render() {
        const container = DOMUtils.$('#settings-page');
        if (!container) return;

        this.loadSettings();

        container.innerHTML = `
            <div class="settings-container">
                <!-- شريط الأدوات -->
                <div class="settings-toolbar">
                    <div class="toolbar-left">
                        <h2>إعدادات النظام</h2>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-success" onclick="settingsManager.saveAllSettings()">
                            <i class="fas fa-save"></i>
                            حفظ جميع الإعدادات
                        </button>
                        <button class="btn btn-warning" onclick="settingsManager.resetToDefaults()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين افتراضي
                        </button>
                    </div>
                </div>

                <!-- تبويبات الإعدادات -->
                <div class="settings-tabs">
                    <div class="tabs-nav">
                        <button class="tab-btn active" onclick="settingsManager.showTab('company')">
                            <i class="fas fa-building"></i>
                            معلومات الشركة
                        </button>
                        <button class="tab-btn" onclick="settingsManager.showTab('system')">
                            <i class="fas fa-cog"></i>
                            إعدادات النظام
                        </button>
                        <button class="tab-btn" onclick="settingsManager.showTab('sales')">
                            <i class="fas fa-shopping-cart"></i>
                            إعدادات المبيعات
                        </button>
                        <button class="tab-btn" onclick="settingsManager.showTab('inventory')">
                            <i class="fas fa-boxes"></i>
                            إعدادات المخزون
                        </button>
                        <button class="tab-btn" onclick="settingsManager.showTab('backup')">
                            <i class="fas fa-database"></i>
                            النسخ الاحتياطي
                        </button>
                        <button class="tab-btn" onclick="settingsManager.showTab('security')">
                            <i class="fas fa-shield-alt"></i>
                            الأمان
                        </button>
                    </div>

                    <!-- تبويب معلومات الشركة -->
                    <div id="company-tab" class="tab-content active">
                        <div class="settings-section">
                            <h3>معلومات الشركة</h3>
                            <form id="company-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>اسم الشركة *</label>
                                        <input type="text" id="company-name" value="${this.settings.company?.name || ''}" required>
                                    </div>
                                    <div class="form-group">
                                        <label>الاسم التجاري</label>
                                        <input type="text" id="company-trade-name" value="${this.settings.company?.tradeName || ''}">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>الرقم الضريبي</label>
                                        <input type="text" id="company-tax-number" value="${this.settings.company?.taxNumber || ''}">
                                    </div>
                                    <div class="form-group">
                                        <label>السجل التجاري</label>
                                        <input type="text" id="company-commercial-register" value="${this.settings.company?.commercialRegister || ''}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>العنوان</label>
                                    <textarea id="company-address" rows="3">${this.settings.company?.address || ''}</textarea>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>الهاتف</label>
                                        <input type="tel" id="company-phone" value="${this.settings.company?.phone || ''}">
                                    </div>
                                    <div class="form-group">
                                        <label>البريد الإلكتروني</label>
                                        <input type="email" id="company-email" value="${this.settings.company?.email || ''}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>شعار الشركة</label>
                                    <div class="logo-upload">
                                        <input type="file" id="company-logo" accept="image/*" onchange="settingsManager.previewLogo()">
                                        <div class="logo-preview" id="logo-preview">
                                            ${this.settings.company?.logo ? 
                                                `<img src="${this.settings.company.logo}" alt="شعار الشركة">` : 
                                                '<i class="fas fa-image"></i><span>اختر شعار الشركة</span>'
                                            }
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- تبويب إعدادات النظام -->
                    <div id="system-tab" class="tab-content">
                        <div class="settings-section">
                            <h3>إعدادات النظام العامة</h3>
                            <form id="system-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>اللغة</label>
                                        <select id="system-language">
                                            <option value="ar" ${this.settings.system?.language === 'ar' ? 'selected' : ''}>العربية</option>
                                            <option value="en" ${this.settings.system?.language === 'en' ? 'selected' : ''}>English</option>
                                            <option value="fr" ${this.settings.system?.language === 'fr' ? 'selected' : ''}>Français</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>الثيم</label>
                                        <select id="system-theme">
                                            <option value="light" ${this.settings.system?.theme === 'light' ? 'selected' : ''}>مضيء</option>
                                            <option value="dark" ${this.settings.system?.theme === 'dark' ? 'selected' : ''}>داكن</option>
                                            <option value="auto" ${this.settings.system?.theme === 'auto' ? 'selected' : ''}>تلقائي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>العملة</label>
                                        <select id="system-currency">
                                            <option value="DZD" ${this.settings.system?.currency === 'DZD' ? 'selected' : ''}>دينار جزائري (DZD)</option>
                                            <option value="USD" ${this.settings.system?.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                                            <option value="EUR" ${this.settings.system?.currency === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>معدل الضريبة (%)</label>
                                        <input type="number" id="system-tax-rate" value="${this.settings.system?.taxRate || 19}" min="0" max="100" step="0.01">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>تنسيق التاريخ</label>
                                        <select id="system-date-format">
                                            <option value="dd/mm/yyyy" ${this.settings.system?.dateFormat === 'dd/mm/yyyy' ? 'selected' : ''}>يوم/شهر/سنة</option>
                                            <option value="mm/dd/yyyy" ${this.settings.system?.dateFormat === 'mm/dd/yyyy' ? 'selected' : ''}>شهر/يوم/سنة</option>
                                            <option value="yyyy-mm-dd" ${this.settings.system?.dateFormat === 'yyyy-mm-dd' ? 'selected' : ''}>سنة-شهر-يوم</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>المنطقة الزمنية</label>
                                        <select id="system-timezone">
                                            <option value="Africa/Algiers" ${this.settings.system?.timezone === 'Africa/Algiers' ? 'selected' : ''}>الجزائر</option>
                                            <option value="UTC" ${this.settings.system?.timezone === 'UTC' ? 'selected' : ''}>UTC</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="system-auto-backup" ${this.settings.system?.autoBackup ? 'checked' : ''}>
                                        <span>تفعيل النسخ الاحتياطي التلقائي</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>فترة النسخ الاحتياطي (بالأيام)</label>
                                    <input type="number" id="system-backup-interval" value="${this.settings.system?.backupInterval || 7}" min="1" max="30">
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- تبويب إعدادات المبيعات -->
                    <div id="sales-tab" class="tab-content">
                        <div class="settings-section">
                            <h3>إعدادات المبيعات</h3>
                            <form id="sales-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>بادئة رقم الفاتورة</label>
                                        <input type="text" id="sales-invoice-prefix" value="${this.settings.sales?.invoicePrefix || 'INV'}" maxlength="10">
                                    </div>
                                    <div class="form-group">
                                        <label>رقم الفاتورة التالي</label>
                                        <input type="number" id="sales-next-invoice-number" value="${this.settings.sales?.nextInvoiceNumber || 1}" min="1">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="sales-auto-print" ${this.settings.sales?.autoPrint ? 'checked' : ''}>
                                        <span>طباعة الفاتورة تلقائياً بعد البيع</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="sales-allow-negative-stock" ${this.settings.sales?.allowNegativeStock ? 'checked' : ''}>
                                        <span>السماح بالبيع عند نفاد المخزون</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="sales-require-customer" ${this.settings.sales?.requireCustomer ? 'checked' : ''}>
                                        <span>إجبار اختيار العميل</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>طريقة الدفع الافتراضية</label>
                                    <select id="sales-default-payment-method">
                                        <option value="cash" ${this.settings.sales?.defaultPaymentMethod === 'cash' ? 'selected' : ''}>نقداً</option>
                                        <option value="credit" ${this.settings.sales?.defaultPaymentMethod === 'credit' ? 'selected' : ''}>على الحساب</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- تبويب إعدادات المخزون -->
                    <div id="inventory-tab" class="tab-content">
                        <div class="settings-section">
                            <h3>إعدادات المخزون</h3>
                            <form id="inventory-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>الحد الأدنى للمخزون (افتراضي)</label>
                                        <input type="number" id="inventory-low-stock-alert" value="${this.settings.system?.lowStockAlert || 10}" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label>طريقة تقييم المخزون</label>
                                        <select id="inventory-valuation-method">
                                            <option value="fifo" ${this.settings.inventory?.valuationMethod === 'fifo' ? 'selected' : ''}>FIFO (الوارد أولاً صادر أولاً)</option>
                                            <option value="lifo" ${this.settings.inventory?.valuationMethod === 'lifo' ? 'selected' : ''}>LIFO (الوارد أخيراً صادر أولاً)</option>
                                            <option value="average" ${this.settings.inventory?.valuationMethod === 'average' ? 'selected' : ''}>المتوسط المرجح</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="inventory-track-expiry" ${this.settings.inventory?.trackExpiry ? 'checked' : ''}>
                                        <span>تتبع تواريخ انتهاء الصلاحية</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="inventory-auto-reorder" ${this.settings.inventory?.autoReorder ? 'checked' : ''}>
                                        <span>تنبيهات إعادة الطلب التلقائية</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>أيام التنبيه قبل انتهاء الصلاحية</label>
                                    <input type="number" id="inventory-expiry-alert-days" value="${this.settings.inventory?.expiryAlertDays || 30}" min="1">
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- تبويب النسخ الاحتياطي -->
                    <div id="backup-tab" class="tab-content">
                        <div class="settings-section">
                            <h3>إدارة النسخ الاحتياطي</h3>
                            <div class="backup-actions">
                                <div class="backup-card">
                                    <div class="backup-icon">
                                        <i class="fas fa-download"></i>
                                    </div>
                                    <div class="backup-content">
                                        <h4>إنشاء نسخة احتياطية</h4>
                                        <p>تحميل نسخة احتياطية من جميع البيانات</p>
                                        <button class="btn btn-primary" onclick="settingsManager.createBackup()">
                                            <i class="fas fa-download"></i>
                                            إنشاء نسخة احتياطية
                                        </button>
                                    </div>
                                </div>

                                <div class="backup-card">
                                    <div class="backup-icon">
                                        <i class="fas fa-upload"></i>
                                    </div>
                                    <div class="backup-content">
                                        <h4>استعادة نسخة احتياطية</h4>
                                        <p>استعادة البيانات من نسخة احتياطية</p>
                                        <input type="file" id="backup-file" accept=".json" style="display: none;" onchange="settingsManager.restoreBackup()">
                                        <button class="btn btn-warning" onclick="DOMUtils.$('#backup-file').click()">
                                            <i class="fas fa-upload"></i>
                                            استعادة نسخة احتياطية
                                        </button>
                                    </div>
                                </div>

                                <div class="backup-card">
                                    <div class="backup-icon">
                                        <i class="fas fa-trash"></i>
                                    </div>
                                    <div class="backup-content">
                                        <h4>مسح جميع البيانات</h4>
                                        <p>حذف جميع البيانات وإعادة تعيين النظام</p>
                                        <button class="btn btn-danger" onclick="settingsManager.clearAllData()">
                                            <i class="fas fa-trash"></i>
                                            مسح جميع البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب الأمان -->
                    <div id="security-tab" class="tab-content">
                        <div class="settings-section">
                            <h3>إعدادات الأمان</h3>
                            <form id="security-form">
                                <div class="form-group">
                                    <label>كلمة المرور الحالية</label>
                                    <input type="password" id="current-password" placeholder="أدخل كلمة المرور الحالية">
                                </div>
                                <div class="form-group">
                                    <label>كلمة المرور الجديدة</label>
                                    <input type="password" id="new-password" placeholder="أدخل كلمة المرور الجديدة">
                                </div>
                                <div class="form-group">
                                    <label>تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" id="confirm-password" placeholder="أعد إدخال كلمة المرور الجديدة">
                                </div>
                                <div class="form-group">
                                    <button type="button" class="btn btn-primary" onclick="settingsManager.changePassword()">
                                        <i class="fas fa-key"></i>
                                        تغيير كلمة المرور
                                    </button>
                                </div>
                                
                                <hr>
                                
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="security-auto-logout" ${this.settings.security?.autoLogout ? 'checked' : ''}>
                                        <span>تسجيل خروج تلقائي عند عدم النشاط</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>مدة عدم النشاط (بالدقائق)</label>
                                    <input type="number" id="security-logout-time" value="${this.settings.security?.logoutTime || 30}" min="5" max="120">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مستمعي تغيير الثيم
        const themeSelect = DOMUtils.$('#system-theme');
        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.applyTheme(e.target.value);
            });
        }
    }

    // عرض التبويبات
    showTab(tabName) {
        // إخفاء جميع التبويبات
        const tabs = DOMUtils.$$('.tab-content');
        tabs.forEach(tab => {
            DOMUtils.removeClass(tab, 'active');
        });

        // إزالة الفئة النشطة من جميع الأزرار
        const tabBtns = DOMUtils.$$('.tab-btn');
        tabBtns.forEach(btn => {
            DOMUtils.removeClass(btn, 'active');
        });

        // إظهار التبويب المطلوب
        const targetTab = DOMUtils.$(`#${tabName}-tab`);
        if (targetTab) {
            DOMUtils.addClass(targetTab, 'active');
        }

        // تفعيل الزر المناسب
        const targetBtn = DOMUtils.$(`[onclick="settingsManager.showTab('${tabName}')"]`);
        if (targetBtn) {
            DOMUtils.addClass(targetBtn, 'active');
        }
    }

    // معاينة الشعار
    previewLogo() {
        const fileInput = DOMUtils.$('#company-logo');
        const logoPreview = DOMUtils.$('#logo-preview');

        if (fileInput?.files && fileInput.files[0]) {
            const file = fileInput.files[0];

            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                app.showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                fileInput.value = '';
                return;
            }

            // التحقق من حجم الملف (1MB)
            if (file.size > 1024 * 1024) {
                app.showNotification('حجم الصورة يجب أن يكون أقل من 1 ميجابايت', 'error');
                fileInput.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                if (logoPreview) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" alt="شعار الشركة">`;
                }
            };
            reader.readAsDataURL(file);
        }
    }

    // حفظ جميع الإعدادات
    saveAllSettings() {
        const newSettings = {
            company: this.getCompanySettings(),
            system: this.getSystemSettings(),
            sales: this.getSalesSettings(),
            inventory: this.getInventorySettings(),
            security: this.getSecuritySettings()
        };

        if (db.saveSettings(newSettings)) {
            this.settings = newSettings;
            app.showNotification('تم حفظ الإعدادات بنجاح', 'success');

            // تطبيق الإعدادات الجديدة
            this.applySettings();
        } else {
            app.showNotification('فشل في حفظ الإعدادات', 'error');
        }
    }

    // الحصول على إعدادات الشركة
    getCompanySettings() {
        const logoFile = DOMUtils.$('#company-logo')?.files[0];
        let logoData = this.settings.company?.logo || null;

        if (logoFile) {
            // تحويل الصورة إلى base64
            const reader = new FileReader();
            reader.onload = (e) => {
                logoData = e.target.result;
            };
            reader.readAsDataURL(logoFile);
        }

        return {
            name: DOMUtils.$('#company-name')?.value?.trim() || '',
            tradeName: DOMUtils.$('#company-trade-name')?.value?.trim() || '',
            taxNumber: DOMUtils.$('#company-tax-number')?.value?.trim() || '',
            commercialRegister: DOMUtils.$('#company-commercial-register')?.value?.trim() || '',
            address: DOMUtils.$('#company-address')?.value?.trim() || '',
            phone: DOMUtils.$('#company-phone')?.value?.trim() || '',
            email: DOMUtils.$('#company-email')?.value?.trim() || '',
            logo: logoData
        };
    }

    // الحصول على إعدادات النظام
    getSystemSettings() {
        return {
            language: DOMUtils.$('#system-language')?.value || 'ar',
            theme: DOMUtils.$('#system-theme')?.value || 'light',
            currency: DOMUtils.$('#system-currency')?.value || 'DZD',
            taxRate: parseFloat(DOMUtils.$('#system-tax-rate')?.value || 19),
            dateFormat: DOMUtils.$('#system-date-format')?.value || 'dd/mm/yyyy',
            timezone: DOMUtils.$('#system-timezone')?.value || 'Africa/Algiers',
            autoBackup: DOMUtils.$('#system-auto-backup')?.checked || false,
            backupInterval: parseInt(DOMUtils.$('#system-backup-interval')?.value || 7),
            lowStockAlert: parseInt(DOMUtils.$('#inventory-low-stock-alert')?.value || 10)
        };
    }

    // الحصول على إعدادات المبيعات
    getSalesSettings() {
        return {
            invoicePrefix: DOMUtils.$('#sales-invoice-prefix')?.value?.trim() || 'INV',
            nextInvoiceNumber: parseInt(DOMUtils.$('#sales-next-invoice-number')?.value || 1),
            autoPrint: DOMUtils.$('#sales-auto-print')?.checked || false,
            allowNegativeStock: DOMUtils.$('#sales-allow-negative-stock')?.checked || false,
            requireCustomer: DOMUtils.$('#sales-require-customer')?.checked || false,
            defaultPaymentMethod: DOMUtils.$('#sales-default-payment-method')?.value || 'cash'
        };
    }

    // الحصول على إعدادات المخزون
    getInventorySettings() {
        return {
            valuationMethod: DOMUtils.$('#inventory-valuation-method')?.value || 'fifo',
            trackExpiry: DOMUtils.$('#inventory-track-expiry')?.checked || false,
            autoReorder: DOMUtils.$('#inventory-auto-reorder')?.checked || false,
            expiryAlertDays: parseInt(DOMUtils.$('#inventory-expiry-alert-days')?.value || 30)
        };
    }

    // الحصول على إعدادات الأمان
    getSecuritySettings() {
        return {
            autoLogout: DOMUtils.$('#security-auto-logout')?.checked || false,
            logoutTime: parseInt(DOMUtils.$('#security-logout-time')?.value || 30)
        };
    }

    // تطبيق الإعدادات
    applySettings() {
        // تطبيق الثيم
        this.applyTheme(this.settings.system?.theme || 'light');

        // تطبيق إعدادات أخرى حسب الحاجة
        if (this.settings.system?.autoBackup) {
            this.scheduleAutoBackup();
        }
    }

    // تطبيق الثيم
    applyTheme(theme) {
        const body = document.body;

        // إزالة جميع فئات الثيم
        body.classList.remove('light-theme', 'dark-theme');

        if (theme === 'dark') {
            body.classList.add('dark-theme');
        } else if (theme === 'light') {
            body.classList.add('light-theme');
        } else if (theme === 'auto') {
            // تطبيق الثيم حسب تفضيلات النظام
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('dark-theme');
            } else {
                body.classList.add('light-theme');
            }
        }
    }

    // إعادة تعيين الإعدادات الافتراضية
    resetToDefaults() {
        app.showConfirm(
            'تأكيد إعادة التعيين',
            'هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
            () => {
                const defaultSettings = {
                    company: {
                        name: '',
                        tradeName: '',
                        taxNumber: '',
                        commercialRegister: '',
                        address: '',
                        phone: '',
                        email: '',
                        logo: null
                    },
                    system: {
                        language: 'ar',
                        theme: 'light',
                        currency: 'DZD',
                        taxRate: 19,
                        dateFormat: 'dd/mm/yyyy',
                        timezone: 'Africa/Algiers',
                        autoBackup: false,
                        backupInterval: 7,
                        lowStockAlert: 10
                    },
                    sales: {
                        invoicePrefix: 'INV',
                        nextInvoiceNumber: 1,
                        autoPrint: false,
                        allowNegativeStock: false,
                        requireCustomer: false,
                        defaultPaymentMethod: 'cash'
                    },
                    inventory: {
                        valuationMethod: 'fifo',
                        trackExpiry: false,
                        autoReorder: false,
                        expiryAlertDays: 30
                    },
                    security: {
                        autoLogout: false,
                        logoutTime: 30
                    }
                };

                if (db.saveSettings(defaultSettings)) {
                    app.showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
                    this.render(); // إعادة عرض الصفحة
                } else {
                    app.showNotification('فشل في إعادة تعيين الإعدادات', 'error');
                }
            }
        );
    }

    // تغيير كلمة المرور
    changePassword() {
        const currentPassword = DOMUtils.$('#current-password')?.value;
        const newPassword = DOMUtils.$('#new-password')?.value;
        const confirmPassword = DOMUtils.$('#confirm-password')?.value;

        if (!currentPassword) {
            app.showNotification('يرجى إدخال كلمة المرور الحالية', 'error');
            return;
        }

        if (!newPassword) {
            app.showNotification('يرجى إدخال كلمة المرور الجديدة', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            app.showNotification('كلمة المرور الجديدة غير متطابقة', 'error');
            return;
        }

        if (newPassword.length < 4) {
            app.showNotification('كلمة المرور يجب أن تكون 4 أحرف على الأقل', 'error');
            return;
        }

        // التحقق من كلمة المرور الحالية
        const storedPassword = StorageUtils.get('cm_password', '1234');
        if (StringUtils.hashPassword(currentPassword) !== storedPassword) {
            app.showNotification('كلمة المرور الحالية غير صحيحة', 'error');
            return;
        }

        // حفظ كلمة المرور الجديدة
        const hashedNewPassword = StringUtils.hashPassword(newPassword);
        StorageUtils.set('cm_password', hashedNewPassword);

        // مسح الحقول
        DOMUtils.$('#current-password').value = '';
        DOMUtils.$('#new-password').value = '';
        DOMUtils.$('#confirm-password').value = '';

        app.showNotification('تم تغيير كلمة المرور بنجاح', 'success');
    }

    // إنشاء نسخة احتياطية
    createBackup() {
        try {
            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: {
                    settings: db.getSettings(),
                    categories: db.getCategories(),
                    products: db.getProducts(),
                    customers: db.getCustomers(),
                    suppliers: db.getSuppliers(),
                    sales: db.getSales(),
                    purchases: db.getPurchases()
                }
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const filename = `cybernet-backup-${DateUtils.getCurrentDate()}.json`;

            Utils.downloadFile(dataStr, filename, 'application/json');
            app.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            app.showNotification('فشل في إنشاء النسخة الاحتياطية', 'error');
        }
    }

    // استعادة نسخة احتياطية
    restoreBackup() {
        const fileInput = DOMUtils.$('#backup-file');
        const file = fileInput?.files[0];

        if (!file) {
            app.showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const backupData = JSON.parse(e.target.result);

                if (!backupData.data) {
                    throw new Error('ملف النسخة الاحتياطية غير صحيح');
                }

                app.showConfirm(
                    'تأكيد الاستعادة',
                    'هل تريد استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
                    () => {
                        // استعادة البيانات
                        Object.keys(backupData.data).forEach(key => {
                            StorageUtils.set(`cm_${key}`, backupData.data[key]);
                        });

                        app.showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');

                        // إعادة تحميل الصفحة
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                );
            } catch (error) {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                app.showNotification('ملف النسخة الاحتياطية غير صحيح', 'error');
            }
        };

        reader.readAsText(file);
        fileInput.value = ''; // مسح اختيار الملف
    }

    // مسح جميع البيانات
    clearAllData() {
        app.showConfirm(
            'تأكيد المسح',
            'هل تريد مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
            () => {
                app.showConfirm(
                    'تأكيد نهائي',
                    'هذا سيحذف جميع البيانات نهائياً. هل أنت متأكد؟',
                    () => {
                        // مسح جميع البيانات
                        const keys = ['cm_settings', 'cm_categories', 'cm_products', 'cm_customers', 'cm_suppliers', 'cm_sales', 'cm_purchases'];
                        keys.forEach(key => {
                            localStorage.removeItem(key);
                        });

                        app.showNotification('تم مسح جميع البيانات', 'success');

                        // إعادة تحميل الصفحة
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                );
            }
        );
    }

    // جدولة النسخ الاحتياطي التلقائي
    scheduleAutoBackup() {
        // يمكن إضافة منطق النسخ الاحتياطي التلقائي هنا
        console.log('تم تفعيل النسخ الاحتياطي التلقائي');
    }
}

// إنشاء مثيل من مدير الإعدادات
const settingsManager = new SettingsManager();

// تصدير مدير الإعدادات للاستخدام العام
window.settingsManager = settingsManager;
