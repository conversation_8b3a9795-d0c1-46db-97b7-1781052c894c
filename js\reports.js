/**
 * Cybernet Magasin - نظام التقارير
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class ReportsManager {
    constructor() {
        this.currentReport = null;
        this.reportData = {};
        this.dateRange = {
            from: null,
            to: null
        };
        this.init();
    }

    // تهيئة مدير التقارير
    init() {
        this.setDefaultDateRange();
    }

    // تعيين نطاق التاريخ الافتراضي (الشهر الحالي)
    setDefaultDateRange() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        
        this.dateRange.from = firstDay.toISOString().split('T')[0];
        this.dateRange.to = lastDay.toISOString().split('T')[0];
    }

    // عرض واجهة التقارير
    render() {
        const container = DOMUtils.$('#reports-page');
        if (!container) return;

        container.innerHTML = `
            <div class="reports-container">
                <!-- شريط الأدوات -->
                <div class="reports-toolbar">
                    <div class="toolbar-left">
                        <h2>التقارير والإحصائيات</h2>
                    </div>
                    <div class="toolbar-right">
                        <div class="date-range-selector">
                            <label>من:</label>
                            <input type="date" id="report-date-from" value="${this.dateRange.from}">
                            <label>إلى:</label>
                            <input type="date" id="report-date-to" value="${this.dateRange.to}">
                            <button class="btn btn-primary" onclick="reportsManager.updateDateRange()">
                                <i class="fas fa-sync"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات التقارير السريعة -->
                <div class="reports-cards">
                    <div class="report-card" onclick="reportsManager.generateReport('sales')">
                        <div class="card-icon sales">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <h3>تقرير المبيعات</h3>
                            <p>تفاصيل المبيعات والإيرادات</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.generateReport('purchases')">
                        <div class="card-icon purchases">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="card-content">
                            <h3>تقرير المشتريات</h3>
                            <p>تفاصيل المشتريات والمصروفات</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.generateReport('inventory')">
                        <div class="card-icon inventory">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="card-content">
                            <h3>تقرير المخزون</h3>
                            <p>حالة المخزون والمنتجات</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.generateReport('customers')">
                        <div class="card-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h3>تقرير العملاء</h3>
                            <p>تحليل العملاء والديون</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.generateReport('suppliers')">
                        <div class="card-icon suppliers">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="card-content">
                            <h3>تقرير الموردين</h3>
                            <p>تحليل الموردين والمستحقات</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.generateReport('financial')">
                        <div class="card-icon financial">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="card-content">
                            <h3>التقرير المالي</h3>
                            <p>الأرباح والخسائر</p>
                        </div>
                    </div>
                </div>

                <!-- منطقة عرض التقرير -->
                <div class="report-display" id="report-display" style="display: none;">
                    <div class="report-header">
                        <h3 id="report-title">عنوان التقرير</h3>
                        <div class="report-actions">
                            <button class="btn btn-secondary" onclick="reportsManager.exportReport('pdf')">
                                <i class="fas fa-file-pdf"></i>
                                PDF
                            </button>
                            <button class="btn btn-info" onclick="reportsManager.exportReport('excel')">
                                <i class="fas fa-file-excel"></i>
                                Excel
                            </button>
                            <button class="btn btn-primary" onclick="reportsManager.printReport()">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                            <button class="btn btn-secondary" onclick="reportsManager.closeReport()">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                    <div class="report-content" id="report-content">
                        <!-- سيتم تحميل محتوى التقرير هنا -->
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مستمعي تغيير التاريخ
        const dateFromInput = DOMUtils.$('#report-date-from');
        const dateToInput = DOMUtils.$('#report-date-to');
        
        if (dateFromInput) {
            dateFromInput.addEventListener('change', () => {
                this.dateRange.from = dateFromInput.value;
            });
        }
        
        if (dateToInput) {
            dateToInput.addEventListener('change', () => {
                this.dateRange.to = dateToInput.value;
            });
        }
    }

    // تحديث نطاق التاريخ
    updateDateRange() {
        const dateFromInput = DOMUtils.$('#report-date-from');
        const dateToInput = DOMUtils.$('#report-date-to');
        
        this.dateRange.from = dateFromInput?.value;
        this.dateRange.to = dateToInput?.value;
        
        if (this.currentReport) {
            this.generateReport(this.currentReport);
        }
        
        app.showNotification('تم تحديث نطاق التاريخ', 'success');
    }

    // توليد التقرير
    generateReport(reportType) {
        this.currentReport = reportType;
        
        // تحميل البيانات
        this.loadReportData();
        
        // إظهار منطقة التقرير
        const reportDisplay = DOMUtils.$('#report-display');
        const reportTitle = DOMUtils.$('#report-title');
        const reportContent = DOMUtils.$('#report-content');
        
        if (reportDisplay) {
            reportDisplay.style.display = 'block';
        }
        
        // تحديد عنوان التقرير
        const titles = {
            'sales': 'تقرير المبيعات',
            'purchases': 'تقرير المشتريات',
            'inventory': 'تقرير المخزون',
            'customers': 'تقرير العملاء',
            'suppliers': 'تقرير الموردين',
            'financial': 'التقرير المالي'
        };
        
        if (reportTitle) {
            reportTitle.textContent = titles[reportType] || 'تقرير';
        }
        
        // توليد محتوى التقرير
        if (reportContent) {
            reportContent.innerHTML = this.renderReportContent(reportType);
        }
        
        // التمرير إلى التقرير
        reportDisplay?.scrollIntoView({ behavior: 'smooth' });
    }

    // تحميل بيانات التقرير
    loadReportData() {
        this.reportData = {
            sales: this.getSalesData(),
            purchases: this.getPurchasesData(),
            products: db.getProducts(),
            customers: db.getCustomers(),
            suppliers: db.getSuppliers(),
            categories: db.getCategories()
        };
    }

    // الحصول على بيانات المبيعات
    getSalesData() {
        const sales = db.getSales();
        return sales.filter(sale => {
            const saleDate = sale.createdAt.split('T')[0];
            return saleDate >= this.dateRange.from && saleDate <= this.dateRange.to;
        });
    }

    // الحصول على بيانات المشتريات
    getPurchasesData() {
        const purchases = db.getPurchases();
        return purchases.filter(purchase => {
            const purchaseDate = (purchase.purchaseDate || purchase.createdAt).split('T')[0];
            return purchaseDate >= this.dateRange.from && purchaseDate <= this.dateRange.to;
        });
    }

    // عرض محتوى التقرير
    renderReportContent(reportType) {
        switch (reportType) {
            case 'sales':
                return this.renderSalesReport();
            case 'purchases':
                return this.renderPurchasesReport();
            case 'inventory':
                return this.renderInventoryReport();
            case 'customers':
                return this.renderCustomersReport();
            case 'suppliers':
                return this.renderSuppliersReport();
            case 'financial':
                return this.renderFinancialReport();
            default:
                return '<p>نوع التقرير غير مدعوم</p>';
        }
    }

    // تقرير المبيعات
    renderSalesReport() {
        const sales = this.reportData.sales;
        const totalSales = sales.length;
        const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
        const totalTax = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
        const totalDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
        
        // تجميع المبيعات حسب اليوم
        const dailySales = {};
        sales.forEach(sale => {
            const date = sale.createdAt.split('T')[0];
            if (!dailySales[date]) {
                dailySales[date] = { count: 0, revenue: 0 };
            }
            dailySales[date].count++;
            dailySales[date].revenue += sale.total;
        });
        
        // أفضل المنتجات مبيعاً
        const productSales = {};
        sales.forEach(sale => {
            sale.items.forEach(item => {
                if (!productSales[item.productId]) {
                    productSales[item.productId] = {
                        name: item.name,
                        quantity: 0,
                        revenue: 0
                    };
                }
                productSales[item.productId].quantity += item.quantity;
                productSales[item.productId].revenue += item.total;
            });
        });
        
        const topProducts = Object.values(productSales)
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 10);

        return `
            <div class="sales-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>إجمالي المبيعات</h4>
                            <div class="card-value">${totalSales}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الإيرادات</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalRevenue)}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الضرائب</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalTax)}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الخصومات</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalDiscount)}</div>
                        </div>
                    </div>
                </div>
                
                <div class="report-section">
                    <h4>المبيعات اليومية</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>عدد المبيعات</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(dailySales).map(([date, data]) => `
                                <tr>
                                    <td>${DateUtils.formatDate(date)}</td>
                                    <td>${data.count}</td>
                                    <td>${NumberUtils.formatCurrency(data.revenue)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="report-section">
                    <h4>أفضل المنتجات مبيعاً</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية المباعة</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${topProducts.map(product => `
                                <tr>
                                    <td>${product.name}</td>
                                    <td>${product.quantity}</td>
                                    <td>${NumberUtils.formatCurrency(product.revenue)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="report-section">
                    <h4>تفاصيل المبيعات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sales.map(sale => {
                                const customer = this.reportData.customers.find(c => c.id === sale.customerId);
                                return `
                                    <tr>
                                        <td>${sale.saleNumber}</td>
                                        <td>${DateUtils.formatDate(sale.createdAt)}</td>
                                        <td>${customer ? customer.name : 'ضيف'}</td>
                                        <td>${NumberUtils.formatCurrency(sale.total)}</td>
                                        <td>${sale.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // تقرير المشتريات
    renderPurchasesReport() {
        const purchases = this.reportData.purchases;
        const totalPurchases = purchases.length;
        const totalCost = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const totalTax = purchases.reduce((sum, purchase) => sum + (purchase.tax || 0), 0);

        return `
            <div class="purchases-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>إجمالي المشتريات</h4>
                            <div class="card-value">${totalPurchases}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي التكلفة</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalCost)}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الضرائب</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalTax)}</div>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h4>تفاصيل المشتريات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الشراء</th>
                                <th>المورد</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchases.map(purchase => {
                                const supplier = this.reportData.suppliers.find(s => s.id === purchase.supplierId);
                                return `
                                    <tr>
                                        <td>${purchase.purchaseNumber}</td>
                                        <td>${supplier ? supplier.name : 'غير محدد'}</td>
                                        <td>${DateUtils.formatDate(purchase.purchaseDate || purchase.createdAt)}</td>
                                        <td>${NumberUtils.formatCurrency(purchase.total)}</td>
                                        <td>${purchase.status === 'received' ? 'مستلم' : purchase.status === 'pending' ? 'في الانتظار' : 'استلام جزئي'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // تقرير المخزون
    renderInventoryReport() {
        const products = this.reportData.products;
        const activeProducts = products.filter(p => p.active);
        const lowStockProducts = products.filter(p => p.quantity <= (p.minStock || 10));
        const outOfStockProducts = products.filter(p => p.quantity === 0);
        const totalValue = products.reduce((sum, product) => sum + (product.quantity * product.price), 0);

        // تجميع حسب الفئات
        const categoryStats = {};
        products.forEach(product => {
            const category = this.reportData.categories.find(c => c.id === product.categoryId);
            const categoryName = category ? category.name : 'غير مصنف';

            if (!categoryStats[categoryName]) {
                categoryStats[categoryName] = {
                    count: 0,
                    value: 0,
                    quantity: 0
                };
            }

            categoryStats[categoryName].count++;
            categoryStats[categoryName].value += product.quantity * product.price;
            categoryStats[categoryName].quantity += product.quantity;
        });

        return `
            <div class="inventory-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>إجمالي المنتجات</h4>
                            <div class="card-value">${products.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>المنتجات النشطة</h4>
                            <div class="card-value">${activeProducts.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>مخزون منخفض</h4>
                            <div class="card-value">${lowStockProducts.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>نفد المخزون</h4>
                            <div class="card-value">${outOfStockProducts.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>قيمة المخزون</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalValue)}</div>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h4>إحصائيات الفئات</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الفئة</th>
                                <th>عدد المنتجات</th>
                                <th>إجمالي الكمية</th>
                                <th>قيمة المخزون</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(categoryStats).map(([category, stats]) => `
                                <tr>
                                    <td>${category}</td>
                                    <td>${stats.count}</td>
                                    <td>${stats.quantity}</td>
                                    <td>${NumberUtils.formatCurrency(stats.value)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                ${lowStockProducts.length > 0 ? `
                    <div class="report-section">
                        <h4>منتجات بمخزون منخفض</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المتاحة</th>
                                    <th>الحد الأدنى</th>
                                    <th>السعر</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${lowStockProducts.map(product => `
                                    <tr>
                                        <td>${product.name}</td>
                                        <td class="low-stock">${product.quantity}</td>
                                        <td>${product.minStock || 10}</td>
                                        <td>${NumberUtils.formatCurrency(product.price)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // تقرير العملاء
    renderCustomersReport() {
        const customers = this.reportData.customers;
        const activeCustomers = customers.filter(c => c.active && !c.isDefault);
        const debtorCustomers = customers.filter(c => c.balance > 0);
        const creditorCustomers = customers.filter(c => c.balance < 0);
        const totalDebt = debtorCustomers.reduce((sum, customer) => sum + customer.balance, 0);
        const totalCredit = Math.abs(creditorCustomers.reduce((sum, customer) => sum + customer.balance, 0));

        return `
            <div class="customers-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>إجمالي العملاء</h4>
                            <div class="card-value">${activeCustomers.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>عملاء مدينون</h4>
                            <div class="card-value">${debtorCustomers.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الديون</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalDebt)}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي الأرصدة الدائنة</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalCredit)}</div>
                        </div>
                    </div>
                </div>

                ${debtorCustomers.length > 0 ? `
                    <div class="report-section">
                        <h4>العملاء المدينون</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>الهاتف</th>
                                    <th>المبلغ المستحق</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${debtorCustomers.map(customer => `
                                    <tr>
                                        <td>${customer.name}</td>
                                        <td>${customer.phone || '-'}</td>
                                        <td class="debt-amount">${NumberUtils.formatCurrency(customer.balance)}</td>
                                        <td>${DateUtils.formatDate(customer.createdAt)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}

                <div class="report-section">
                    <h4>جميع العملاء</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>الهاتف</th>
                                <th>الرصيد</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${activeCustomers.map(customer => `
                                <tr>
                                    <td>${customer.name}</td>
                                    <td>${customer.phone || '-'}</td>
                                    <td class="${customer.balance > 0 ? 'debt-amount' : customer.balance < 0 ? 'credit-amount' : ''}">${NumberUtils.formatCurrency(Math.abs(customer.balance))} ${customer.balance > 0 ? '(مدين)' : customer.balance < 0 ? '(دائن)' : ''}</td>
                                    <td><span class="status-badge ${customer.active ? 'active' : 'inactive'}">${customer.active ? 'نشط' : 'غير نشط'}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // تقرير الموردين
    renderSuppliersReport() {
        const suppliers = this.reportData.suppliers;
        const activeSuppliers = suppliers.filter(s => s.active);
        const payableSuppliers = suppliers.filter(s => s.balance > 0);
        const totalPayables = payableSuppliers.reduce((sum, supplier) => sum + supplier.balance, 0);

        return `
            <div class="suppliers-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>إجمالي الموردين</h4>
                            <div class="card-value">${activeSuppliers.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>موردين بمستحقات</h4>
                            <div class="card-value">${payableSuppliers.length}</div>
                        </div>
                        <div class="summary-card">
                            <h4>إجمالي المستحقات</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalPayables)}</div>
                        </div>
                    </div>
                </div>

                ${payableSuppliers.length > 0 ? `
                    <div class="report-section">
                        <h4>الموردين بمستحقات</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الشركة</th>
                                    <th>الهاتف</th>
                                    <th>المبلغ المستحق</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${payableSuppliers.map(supplier => `
                                    <tr>
                                        <td>${supplier.name}</td>
                                        <td>${supplier.company || '-'}</td>
                                        <td>${supplier.phone || '-'}</td>
                                        <td class="payable-amount">${NumberUtils.formatCurrency(supplier.balance)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}

                <div class="report-section">
                    <h4>جميع الموردين</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>الشركة</th>
                                <th>الهاتف</th>
                                <th>الرصيد</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${activeSuppliers.map(supplier => `
                                <tr>
                                    <td>${supplier.name}</td>
                                    <td>${supplier.company || '-'}</td>
                                    <td>${supplier.phone || '-'}</td>
                                    <td class="${supplier.balance > 0 ? 'payable-amount' : ''}">${NumberUtils.formatCurrency(supplier.balance)}</td>
                                    <td><span class="status-badge ${supplier.active ? 'active' : 'inactive'}">${supplier.active ? 'نشط' : 'غير نشط'}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // التقرير المالي
    renderFinancialReport() {
        const sales = this.reportData.sales;
        const purchases = this.reportData.purchases;

        // حساب الإيرادات
        const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
        const totalSalesTax = sales.reduce((sum, sale) => sum + (sale.tax || 0), 0);
        const totalSalesDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
        const netRevenue = totalRevenue - totalSalesTax - totalSalesDiscount;

        // حساب التكاليف
        const totalCosts = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
        const totalPurchasesTax = purchases.reduce((sum, purchase) => sum + (purchase.tax || 0), 0);
        const netCosts = totalCosts - totalPurchasesTax;

        // حساب الأرباح
        const grossProfit = netRevenue - netCosts;
        const profitMargin = netRevenue > 0 ? (grossProfit / netRevenue) * 100 : 0;

        // حساب الديون والمستحقات
        const totalCustomerDebt = this.reportData.customers.reduce((sum, customer) =>
            sum + (customer.balance > 0 ? customer.balance : 0), 0);
        const totalSupplierPayables = this.reportData.suppliers.reduce((sum, supplier) =>
            sum + (supplier.balance > 0 ? supplier.balance : 0), 0);

        // صافي الأصول
        const netAssets = totalCustomerDebt - totalSupplierPayables;

        return `
            <div class="financial-report">
                <div class="report-summary">
                    <div class="summary-cards">
                        <div class="summary-card revenue">
                            <h4>إجمالي الإيرادات</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalRevenue)}</div>
                        </div>
                        <div class="summary-card costs">
                            <h4>إجمالي التكاليف</h4>
                            <div class="card-value">${NumberUtils.formatCurrency(totalCosts)}</div>
                        </div>
                        <div class="summary-card profit">
                            <h4>إجمالي الأرباح</h4>
                            <div class="card-value ${grossProfit >= 0 ? 'positive' : 'negative'}">${NumberUtils.formatCurrency(grossProfit)}</div>
                        </div>
                        <div class="summary-card margin">
                            <h4>هامش الربح</h4>
                            <div class="card-value">${profitMargin.toFixed(2)}%</div>
                        </div>
                    </div>
                </div>

                <div class="financial-sections">
                    <div class="financial-section">
                        <h4>بيان الدخل</h4>
                        <table class="table financial-table">
                            <tbody>
                                <tr class="section-header">
                                    <td><strong>الإيرادات</strong></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>إجمالي المبيعات</td>
                                    <td class="amount">${NumberUtils.formatCurrency(totalRevenue)}</td>
                                </tr>
                                <tr>
                                    <td>خصم المبيعات</td>
                                    <td class="amount negative">(${NumberUtils.formatCurrency(totalSalesDiscount)})</td>
                                </tr>
                                <tr>
                                    <td>ضريبة المبيعات</td>
                                    <td class="amount negative">(${NumberUtils.formatCurrency(totalSalesTax)})</td>
                                </tr>
                                <tr class="subtotal">
                                    <td><strong>صافي الإيرادات</strong></td>
                                    <td class="amount"><strong>${NumberUtils.formatCurrency(netRevenue)}</strong></td>
                                </tr>

                                <tr class="section-header">
                                    <td><strong>التكاليف</strong></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>تكلفة المشتريات</td>
                                    <td class="amount negative">(${NumberUtils.formatCurrency(totalCosts)})</td>
                                </tr>
                                <tr>
                                    <td>ضريبة المشتريات</td>
                                    <td class="amount">${NumberUtils.formatCurrency(totalPurchasesTax)}</td>
                                </tr>
                                <tr class="subtotal">
                                    <td><strong>صافي التكاليف</strong></td>
                                    <td class="amount negative"><strong>(${NumberUtils.formatCurrency(netCosts)})</strong></td>
                                </tr>

                                <tr class="total">
                                    <td><strong>إجمالي الأرباح/الخسائر</strong></td>
                                    <td class="amount ${grossProfit >= 0 ? 'positive' : 'negative'}"><strong>${NumberUtils.formatCurrency(grossProfit)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="financial-section">
                        <h4>الميزانية العمومية</h4>
                        <table class="table financial-table">
                            <tbody>
                                <tr class="section-header">
                                    <td><strong>الأصول</strong></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>ذمم العملاء</td>
                                    <td class="amount">${NumberUtils.formatCurrency(totalCustomerDebt)}</td>
                                </tr>
                                <tr>
                                    <td>قيمة المخزون</td>
                                    <td class="amount">${NumberUtils.formatCurrency(this.calculateInventoryValue())}</td>
                                </tr>
                                <tr class="subtotal">
                                    <td><strong>إجمالي الأصول</strong></td>
                                    <td class="amount"><strong>${NumberUtils.formatCurrency(totalCustomerDebt + this.calculateInventoryValue())}</strong></td>
                                </tr>

                                <tr class="section-header">
                                    <td><strong>الخصوم</strong></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>مستحقات الموردين</td>
                                    <td class="amount negative">(${NumberUtils.formatCurrency(totalSupplierPayables)})</td>
                                </tr>
                                <tr class="subtotal">
                                    <td><strong>إجمالي الخصوم</strong></td>
                                    <td class="amount negative"><strong>(${NumberUtils.formatCurrency(totalSupplierPayables)})</strong></td>
                                </tr>

                                <tr class="total">
                                    <td><strong>صافي الأصول</strong></td>
                                    <td class="amount ${netAssets >= 0 ? 'positive' : 'negative'}"><strong>${NumberUtils.formatCurrency(netAssets + this.calculateInventoryValue())}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="financial-analysis">
                    <h4>التحليل المالي</h4>
                    <div class="analysis-grid">
                        <div class="analysis-item">
                            <label>نسبة الربحية:</label>
                            <span class="${profitMargin >= 0 ? 'positive' : 'negative'}">${profitMargin.toFixed(2)}%</span>
                        </div>
                        <div class="analysis-item">
                            <label>متوسط قيمة البيع:</label>
                            <span>${sales.length > 0 ? NumberUtils.formatCurrency(totalRevenue / sales.length) : NumberUtils.formatCurrency(0)}</span>
                        </div>
                        <div class="analysis-item">
                            <label>متوسط قيمة الشراء:</label>
                            <span>${purchases.length > 0 ? NumberUtils.formatCurrency(totalCosts / purchases.length) : NumberUtils.formatCurrency(0)}</span>
                        </div>
                        <div class="analysis-item">
                            <label>نسبة الديون للإيرادات:</label>
                            <span>${totalRevenue > 0 ? ((totalCustomerDebt / totalRevenue) * 100).toFixed(2) : 0}%</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // حساب قيمة المخزون
    calculateInventoryValue() {
        return this.reportData.products.reduce((sum, product) =>
            sum + (product.quantity * product.price), 0);
    }

    // إغلاق التقرير
    closeReport() {
        const reportDisplay = DOMUtils.$('#report-display');
        if (reportDisplay) {
            reportDisplay.style.display = 'none';
        }
        this.currentReport = null;
    }

    // طباعة التقرير
    printReport() {
        window.print();
    }

    // تصدير التقرير
    exportReport(format) {
        if (!this.currentReport) {
            app.showNotification('لا يوجد تقرير لتصديره', 'warning');
            return;
        }

        const reportContent = DOMUtils.$('#report-content');
        if (!reportContent) return;

        const reportTitle = DOMUtils.$('#report-title')?.textContent || 'تقرير';
        const dateRange = `من ${DateUtils.formatDate(this.dateRange.from)} إلى ${DateUtils.formatDate(this.dateRange.to)}`;

        if (format === 'pdf') {
            // يمكن إضافة منطق تصدير PDF هنا
            app.showNotification('تصدير PDF قيد التطوير', 'info');
        } else if (format === 'excel') {
            // تصدير كـ CSV (بديل عن Excel)
            this.exportAsCSV(reportTitle, dateRange);
        }
    }

    // تصدير كـ CSV
    exportAsCSV(title, dateRange) {
        let csvContent = `${title}\n${dateRange}\n\n`;

        // استخراج البيانات من الجداول
        const tables = DOMUtils.$$('#report-content table');
        tables.forEach((table, index) => {
            if (index > 0) csvContent += '\n';

            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('th, td');
                const rowData = Array.from(cells).map(cell => {
                    let text = cell.textContent.trim();
                    // إزالة الرموز والتنسيق
                    text = text.replace(/[()]/g, '').replace(/\s+/g, ' ');
                    return `"${text}"`;
                });
                csvContent += rowData.join(',') + '\n';
            });
        });

        const filename = `${title.replace(/\s+/g, '-')}-${DateUtils.getCurrentDate()}.csv`;
        Utils.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
        app.showNotification('تم تصدير التقرير بنجاح', 'success');
    }
}

// إنشاء مثيل من مدير التقارير
const reportsManager = new ReportsManager();

// تصدير مدير التقارير للاستخدام العام
window.reportsManager = reportsManager;
