/**
 * Cybernet Magasin - الوظائف المساعدة
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

// وظائف التاريخ والوقت
const DateUtils = {
    // تنسيق التاريخ بالعربية
    formatDate(date, format = 'dd/mm/yyyy') {
        if (!date) return '';
        
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        switch (format) {
            case 'dd/mm/yyyy':
                return `${day}/${month}/${year}`;
            case 'yyyy-mm-dd':
                return `${year}-${month}-${day}`;
            case 'dd/mm/yyyy hh:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'arabic':
                const arabicMonths = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];
                return `${day} ${arabicMonths[d.getMonth()]} ${year}`;
            default:
                return d.toLocaleDateString('ar-DZ');
        }
    },
    
    // الحصول على التاريخ الحالي
    getCurrentDate() {
        return new Date().toISOString().split('T')[0];
    },
    
    // الحصول على الوقت الحالي
    getCurrentTime() {
        return new Date().toLocaleTimeString('ar-DZ', { hour12: false });
    },
    
    // الحصول على التاريخ والوقت الحالي
    getCurrentDateTime() {
        return new Date().toISOString();
    },
    
    // حساب الفرق بين تاريخين
    dateDiff(date1, date2, unit = 'days') {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        const diffTime = Math.abs(d2 - d1);
        
        switch (unit) {
            case 'days':
                return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            case 'hours':
                return Math.ceil(diffTime / (1000 * 60 * 60));
            case 'minutes':
                return Math.ceil(diffTime / (1000 * 60));
            default:
                return diffTime;
        }
    }
};

// وظائف الأرقام والعملة
const NumberUtils = {
    // تنسيق الأرقام بالفواصل
    formatNumber(number, decimals = 2) {
        if (isNaN(number)) return '0.00';
        return parseFloat(number).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    // تنسيق العملة
    formatCurrency(amount, currency = 'DZD') {
        if (isNaN(amount)) return '0.00 دج';
        const formatted = this.formatNumber(amount, 2);
        return `${formatted} دج`;
    },
    
    // تحويل الرقم إلى كلمات (عربي)
    numberToWords(number) {
        const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
        const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
        const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
        
        if (number === 0) return 'صفر';
        if (number < 0) return 'سالب ' + this.numberToWords(-number);
        
        let result = '';
        
        if (number >= 1000) {
            result += this.numberToWords(Math.floor(number / 1000)) + ' ألف ';
            number %= 1000;
        }
        
        if (number >= 100) {
            result += ones[Math.floor(number / 100)] + ' مائة ';
            number %= 100;
        }
        
        if (number >= 20) {
            result += tens[Math.floor(number / 10)];
            if (number % 10 !== 0) {
                result += ' ' + ones[number % 10];
            }
        } else if (number >= 10) {
            result += teens[number - 10];
        } else if (number > 0) {
            result += ones[number];
        }
        
        return result.trim();
    },
    
    // حساب النسبة المئوية
    calculatePercentage(value, total) {
        if (total === 0) return 0;
        return (value / total) * 100;
    },
    
    // تطبيق الخصم
    applyDiscount(amount, discount, isPercentage = true) {
        if (isPercentage) {
            return amount - (amount * discount / 100);
        }
        return amount - discount;
    },
    
    // حساب الضريبة
    calculateTax(amount, taxRate) {
        return amount * taxRate / 100;
    }
};

// وظائف النصوص
const StringUtils = {
    // تنظيف النص
    cleanText(text) {
        if (!text) return '';
        return text.toString().trim().replace(/\s+/g, ' ');
    },
    
    // تحويل إلى أحرف كبيرة
    toUpperCase(text) {
        return this.cleanText(text).toUpperCase();
    },
    
    // تحويل إلى أحرف صغيرة
    toLowerCase(text) {
        return this.cleanText(text).toLowerCase();
    },
    
    // تحويل الحرف الأول إلى كبير
    capitalize(text) {
        const cleaned = this.cleanText(text);
        return cleaned.charAt(0).toUpperCase() + cleaned.slice(1).toLowerCase();
    },
    
    // اقتطاع النص
    truncate(text, length = 50, suffix = '...') {
        const cleaned = this.cleanText(text);
        if (cleaned.length <= length) return cleaned;
        return cleaned.substring(0, length) + suffix;
    },
    
    // إزالة الأحرف الخاصة
    removeSpecialChars(text) {
        return text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\w\s]/g, '');
    },
    
    // التحقق من صحة البريد الإلكتروني
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // التحقق من صحة رقم الهاتف
    isValidPhone(phone) {
        const phoneRegex = /^[0-9+\-\s()]{8,15}$/;
        return phoneRegex.test(phone);
    },
    
    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};

// وظائف التحقق من صحة البيانات
const ValidationUtils = {
    // التحقق من وجود القيمة
    required(value, message = 'هذا الحقل مطلوب') {
        if (!value || value.toString().trim() === '') {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من الحد الأدنى للطول
    minLength(value, min, message = `يجب أن يكون الطول ${min} أحرف على الأقل`) {
        if (value && value.toString().length < min) {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من الحد الأقصى للطول
    maxLength(value, max, message = `يجب أن لا يتجاوز الطول ${max} حرف`) {
        if (value && value.toString().length > max) {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من كون القيمة رقم
    isNumber(value, message = 'يجب أن تكون القيمة رقم') {
        if (value && isNaN(value)) {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من كون الرقم موجب
    isPositive(value, message = 'يجب أن يكون الرقم موجب') {
        if (value && parseFloat(value) <= 0) {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من البريد الإلكتروني
    email(value, message = 'البريد الإلكتروني غير صحيح') {
        if (value && !StringUtils.isValidEmail(value)) {
            return { valid: false, message };
        }
        return { valid: true };
    },
    
    // التحقق من رقم الهاتف
    phone(value, message = 'رقم الهاتف غير صحيح') {
        if (value && !StringUtils.isValidPhone(value)) {
            return { valid: false, message };
        }
        return { valid: true };
    }
};

// وظائف DOM
const DOMUtils = {
    // الحصول على عنصر
    $(selector) {
        return document.querySelector(selector);
    },
    
    // الحصول على عناصر متعددة
    $$(selector) {
        return document.querySelectorAll(selector);
    },
    
    // إضافة مستمع حدث
    on(element, event, handler) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.addEventListener(event, handler);
        }
    },
    
    // إزالة مستمع حدث
    off(element, event, handler) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.removeEventListener(event, handler);
        }
    },
    
    // إضافة فئة CSS
    addClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.add(className);
        }
    },
    
    // إزالة فئة CSS
    removeClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.remove(className);
        }
    },
    
    // تبديل فئة CSS
    toggleClass(element, className) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            element.classList.toggle(className);
        }
    },
    
    // إظهار عنصر
    show(element) {
        this.removeClass(element, 'hidden');
        this.addClass(element, 'visible');
    },
    
    // إخفاء عنصر
    hide(element) {
        this.removeClass(element, 'visible');
        this.addClass(element, 'hidden');
    },
    
    // تبديل إظهار/إخفاء عنصر
    toggle(element) {
        if (typeof element === 'string') {
            element = this.$(element);
        }
        if (element) {
            if (element.classList.contains('hidden')) {
                this.show(element);
            } else {
                this.hide(element);
            }
        }
    }
};

// وظائف التخزين المحلي
const StorageUtils = {
    // حفظ البيانات
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },
    
    // استرجاع البيانات
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return defaultValue;
        }
    },
    
    // حذف البيانات
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    },
    
    // مسح جميع البيانات
    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    },
    
    // التحقق من وجود مفتاح
    exists(key) {
        return localStorage.getItem(key) !== null;
    }
};

// وظائف عامة مساعدة
const Utils = {
    // تأخير التنفيذ
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    // نسخ النص إلى الحافظة
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            console.error('خطأ في نسخ النص:', error);
            return false;
        }
    },
    
    // تحميل ملف
    downloadFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },
    
    // قراءة ملف
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    },
    
    // تشفير بسيط
    simpleEncrypt(text, key = 'cybernet') {
        let result = '';
        for (let i = 0; i < text.length; i++) {
            result += String.fromCharCode(
                text.charCodeAt(i) ^ key.charCodeAt(i % key.length)
            );
        }
        return btoa(result);
    },
    
    // فك التشفير البسيط
    simpleDecrypt(encrypted, key = 'cybernet') {
        try {
            const text = atob(encrypted);
            let result = '';
            for (let i = 0; i < text.length; i++) {
                result += String.fromCharCode(
                    text.charCodeAt(i) ^ key.charCodeAt(i % key.length)
                );
            }
            return result;
        } catch (error) {
            return null;
        }
    }
};

// تصدير الوظائف للاستخدام العام
window.DateUtils = DateUtils;
window.NumberUtils = NumberUtils;
window.StringUtils = StringUtils;
window.ValidationUtils = ValidationUtils;
window.DOMUtils = DOMUtils;
window.StorageUtils = StorageUtils;
window.Utils = Utils;
