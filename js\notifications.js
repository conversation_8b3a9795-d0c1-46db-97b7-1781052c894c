/**
 * Cybernet Magasin - نظام الإشعارات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 50;
        this.checkInterval = 5 * 60 * 1000; // 5 دقائق
        this.init();
    }

    // تهيئة نظام الإشعارات
    init() {
        this.loadNotifications();
        this.startPeriodicCheck();
        this.setupEventListeners();
    }

    // تحميل الإشعارات المحفوظة
    loadNotifications() {
        this.notifications = StorageUtils.get('cm_notifications', []);
        this.updateNotificationCount();
    }

    // حفظ الإشعارات
    saveNotifications() {
        StorageUtils.set('cm_notifications', this.notifications);
    }

    // بدء الفحص الدوري
    startPeriodicCheck() {
        // فحص فوري
        this.checkAllNotifications();
        
        // فحص دوري
        setInterval(() => {
            this.checkAllNotifications();
        }, this.checkInterval);
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مستمع تحديث البيانات
        document.addEventListener('dataUpdated', () => {
            this.checkAllNotifications();
        });
    }

    // فحص جميع أنواع الإشعارات
    checkAllNotifications() {
        this.checkLowStock();
        this.checkOverduePayments();
        this.checkSupplierPayables();
        this.checkExpiringProducts();
        this.updateNotificationCount();
    }

    // فحص المخزون المنخفض
    checkLowStock() {
        const products = db.getProducts();
        const settings = db.getSettings();
        const lowStockAlert = settings.system?.lowStockAlert || 10;
        
        const lowStockProducts = products.filter(product => 
            product.quantity <= lowStockAlert && product.active
        );
        
        if (lowStockProducts.length > 0) {
            const notification = {
                id: 'low-stock',
                type: 'warning',
                title: 'تنبيه مخزون منخفض',
                message: `يوجد ${lowStockProducts.length} منتج بمخزون منخفض`,
                details: lowStockProducts.map(p => `${p.name}: ${p.quantity} متبقي`),
                timestamp: new Date().toISOString(),
                persistent: true
            };
            
            this.addNotification(notification);
        } else {
            this.removeNotification('low-stock');
        }
    }

    // فحص المدفوعات المتأخرة
    checkOverduePayments() {
        const customers = db.getCustomers();
        const overdueCustomers = customers.filter(customer => 
            customer.balance > 0 && !customer.isDefault
        );
        
        if (overdueCustomers.length > 0) {
            const totalDebt = overdueCustomers.reduce((sum, customer) => sum + customer.balance, 0);
            
            const notification = {
                id: 'overdue-payments',
                type: 'danger',
                title: 'ديون مستحقة',
                message: `يوجد ${overdueCustomers.length} عميل بإجمالي ديون ${NumberUtils.formatCurrency(totalDebt)}`,
                details: overdueCustomers.map(c => `${c.name}: ${NumberUtils.formatCurrency(c.balance)}`),
                timestamp: new Date().toISOString(),
                persistent: true
            };
            
            this.addNotification(notification);
        } else {
            this.removeNotification('overdue-payments');
        }
    }

    // فحص مستحقات الموردين
    checkSupplierPayables() {
        const suppliers = db.getSuppliers();
        const payableSuppliers = suppliers.filter(supplier => 
            supplier.balance > 0 && supplier.active
        );
        
        if (payableSuppliers.length > 0) {
            const totalPayables = payableSuppliers.reduce((sum, supplier) => sum + supplier.balance, 0);
            
            const notification = {
                id: 'supplier-payables',
                type: 'info',
                title: 'مستحقات الموردين',
                message: `يوجد ${payableSuppliers.length} مورد بإجمالي مستحقات ${NumberUtils.formatCurrency(totalPayables)}`,
                details: payableSuppliers.map(s => `${s.name}: ${NumberUtils.formatCurrency(s.balance)}`),
                timestamp: new Date().toISOString(),
                persistent: true
            };
            
            this.addNotification(notification);
        } else {
            this.removeNotification('supplier-payables');
        }
    }

    // فحص المنتجات منتهية الصلاحية
    checkExpiringProducts() {
        const products = db.getProducts();
        const settings = db.getSettings();
        const expiryAlertDays = settings.inventory?.expiryAlertDays || 30;
        
        const expiringProducts = products.filter(product => {
            if (!product.expiryDate || !product.active) return false;
            
            const expiryDate = new Date(product.expiryDate);
            const today = new Date();
            const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            
            return daysUntilExpiry <= expiryAlertDays && daysUntilExpiry >= 0;
        });
        
        if (expiringProducts.length > 0) {
            const notification = {
                id: 'expiring-products',
                type: 'warning',
                title: 'منتجات قاربت على انتهاء الصلاحية',
                message: `يوجد ${expiringProducts.length} منتج قارب على انتهاء الصلاحية`,
                details: expiringProducts.map(p => {
                    const daysLeft = Math.ceil((new Date(p.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                    return `${p.name}: ${daysLeft} يوم متبقي`;
                }),
                timestamp: new Date().toISOString(),
                persistent: true
            };
            
            this.addNotification(notification);
        } else {
            this.removeNotification('expiring-products');
        }
    }

    // إضافة إشعار
    addNotification(notification) {
        // التحقق من وجود الإشعار
        const existingIndex = this.notifications.findIndex(n => n.id === notification.id);
        
        if (existingIndex !== -1) {
            // تحديث الإشعار الموجود
            this.notifications[existingIndex] = notification;
        } else {
            // إضافة إشعار جديد
            this.notifications.unshift(notification);
        }
        
        // الاحتفاظ بعدد محدود من الإشعارات
        if (this.notifications.length > this.maxNotifications) {
            this.notifications = this.notifications.slice(0, this.maxNotifications);
        }
        
        this.saveNotifications();
        this.updateNotificationCount();
        
        // إظهار إشعار مؤقت للإشعارات الجديدة
        if (existingIndex === -1 && !notification.persistent) {
            this.showToast(notification);
        }
    }

    // إزالة إشعار
    removeNotification(notificationId) {
        const index = this.notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            this.notifications.splice(index, 1);
            this.saveNotifications();
            this.updateNotificationCount();
        }
    }

    // مسح جميع الإشعارات
    clearAllNotifications() {
        this.notifications = [];
        this.saveNotifications();
        this.updateNotificationCount();
    }

    // تحديث عدد الإشعارات
    updateNotificationCount() {
        const countElement = DOMUtils.$('.notification-count');
        if (countElement) {
            const count = this.notifications.length;
            countElement.textContent = count;
            countElement.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }

    // إظهار إشعار مؤقت
    showToast(notification, duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${notification.type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="toast-text">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                </div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // إضافة الإشعار للصفحة
        document.body.appendChild(toast);
        
        // إظهار الإشعار
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // إخفاء الإشعار تلقائياً
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.parentElement.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-exclamation-circle',
            error: 'fas fa-times-circle'
        };
        return icons[type] || 'fas fa-bell';
    }

    // الحصول على جميع الإشعارات
    getAllNotifications() {
        return this.notifications;
    }

    // الحصول على الإشعارات حسب النوع
    getNotificationsByType(type) {
        return this.notifications.filter(n => n.type === type);
    }

    // تحديد الإشعار كمقروء
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.saveNotifications();
        }
    }

    // تحديد جميع الإشعارات كمقروءة
    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.saveNotifications();
    }

    // الحصول على عدد الإشعارات غير المقروءة
    getUnreadCount() {
        return this.notifications.filter(n => !n.read).length;
    }

    // إنشاء إشعار مخصص
    createCustomNotification(title, message, type = 'info', persistent = false) {
        const notification = {
            id: StringUtils.generateId(),
            type: type,
            title: title,
            message: message,
            timestamp: new Date().toISOString(),
            persistent: persistent,
            custom: true
        };
        
        this.addNotification(notification);
        return notification.id;
    }

    // عرض نافذة الإشعارات
    showNotificationsModal() {
        const modal = DOMUtils.$('#notifications-modal');
        const notificationsList = DOMUtils.$('#notifications-list');
        
        if (notificationsList) {
            notificationsList.innerHTML = this.renderNotificationsList();
        }
        
        if (modal) {
            DOMUtils.addClass(modal, 'show');
        }
    }

    // عرض قائمة الإشعارات
    renderNotificationsList() {
        if (this.notifications.length === 0) {
            return '<p class="text-center text-secondary">لا توجد إشعارات</p>';
        }
        
        return this.notifications.map(notification => `
            <div class="notification-item ${notification.type} ${notification.read ? 'read' : 'unread'}">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                    ${notification.details ? `
                        <details class="notification-details">
                            <summary>عرض التفاصيل</summary>
                            <ul>
                                ${notification.details.map(detail => `<li>${detail}</li>`).join('')}
                            </ul>
                        </details>
                    ` : ''}
                    <small class="notification-time">
                        ${DateUtils.formatDate(notification.timestamp, 'dd/mm/yyyy hh:mm')}
                    </small>
                </div>
                <div class="notification-actions">
                    ${!notification.read ? `
                        <button class="btn btn-sm btn-secondary" onclick="notificationSystem.markAsRead('${notification.id}')">
                            تحديد كمقروء
                        </button>
                    ` : ''}
                    ${!notification.persistent ? `
                        <button class="btn btn-sm btn-danger" onclick="notificationSystem.removeNotification('${notification.id}')">
                            حذف
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }
}

// إنشاء مثيل من نظام الإشعارات
const notificationSystem = new NotificationSystem();

// تصدير نظام الإشعارات للاستخدام العام
window.notificationSystem = notificationSystem;
