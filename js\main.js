/**
 * Cybernet Magasin - ملف التهيئة الرئيسي
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

// متغيرات عامة
let isAppReady = false;
let loadingTimeout;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
async function initializeApp() {
    try {
        // إظهار شاشة التحميل
        showLoadingScreen();
        
        // تحميل البيانات الأساسية
        await loadEssentialData();
        
        // تهيئة المكونات
        await initializeComponents();
        
        // تحميل حالة التطبيق المحفوظة
        loadAppState();
        
        // إخفاء شاشة التحميل
        hideLoadingScreen();
        
        // تحديد حالة التطبيق كجاهز
        isAppReady = true;
        
        // إطلاق حدث جاهزية التطبيق
        dispatchAppReadyEvent();
        
        console.log('✅ تم تحميل Cybernet Magasin بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة التطبيق:', error);
        showErrorScreen(error);
    }
}

// إظهار شاشة التحميل
function showLoadingScreen() {
    const loadingScreen = DOMUtils.$('#loading-screen');
    if (loadingScreen) {
        DOMUtils.removeClass(loadingScreen, 'hidden');
        DOMUtils.addClass(loadingScreen, 'visible');
    }
    
    // تعيين مهلة زمنية للتحميل
    loadingTimeout = setTimeout(() => {
        if (!isAppReady) {
            console.warn('⚠️ تحميل التطبيق يستغرق وقتاً أطول من المتوقع');
            updateLoadingMessage('جاري التحميل... يرجى الانتظار');
        }
    }, 5000);
}

// إخفاء شاشة التحميل
function hideLoadingScreen() {
    if (loadingTimeout) {
        clearTimeout(loadingTimeout);
    }
    
    const loadingScreen = DOMUtils.$('#loading-screen');
    if (loadingScreen) {
        // تأثير انتقالي سلس
        setTimeout(() => {
            DOMUtils.addClass(loadingScreen, 'hidden');
            DOMUtils.removeClass(loadingScreen, 'visible');
        }, 500);
    }
}

// تحديث رسالة التحميل
function updateLoadingMessage(message) {
    const loadingMessage = DOMUtils.$('.loading-spinner h3');
    if (loadingMessage) {
        loadingMessage.textContent = message;
    }
}

// تحميل البيانات الأساسية
async function loadEssentialData() {
    updateLoadingMessage('جاري تحميل البيانات الأساسية...');
    
    // التحقق من وجود قاعدة البيانات
    if (!window.db) {
        throw new Error('قاعدة البيانات غير متوفرة');
    }
    
    // التحقق من البيانات الأساسية
    const settings = db.getSettings();
    if (!settings || Object.keys(settings).length === 0) {
        console.log('🔧 إنشاء البيانات الافتراضية...');
        db.createDefaultData();
    }
    
    // تحميل الإعدادات
    await loadSettings();
    
    // تحميل البيانات المخزنة
    await loadStoredData();
    
    await Utils.delay(500); // محاكاة وقت التحميل
}

// تحميل الإعدادات
async function loadSettings() {
    const settings = db.getSettings();
    
    // تطبيق إعدادات الشركة
    if (settings.company) {
        document.title = `${settings.company.name} - نظام إدارة نقاط البيع`;
        
        // تحديث معلومات الشركة في الواجهة
        updateCompanyInfo(settings.company);
    }
    
    // تطبيق إعدادات النظام
    if (settings.system) {
        // تطبيق الثيم
        if (settings.system.theme) {
            document.body.className = `${settings.system.theme}-theme`;
        }
        
        // تطبيق اللغة
        if (settings.system.language) {
            document.documentElement.lang = settings.system.language;
            document.documentElement.dir = settings.system.language === 'ar' ? 'rtl' : 'ltr';
        }
    }
}

// تحديث معلومات الشركة
function updateCompanyInfo(companyInfo) {
    // تحديث الشعار إذا كان متوفراً
    if (companyInfo.logo) {
        const logoElements = DOMUtils.$$('.company-logo');
        logoElements.forEach(logo => {
            logo.src = companyInfo.logo;
            logo.alt = companyInfo.name;
        });
    }
    
    // تحديث اسم الشركة
    const companyNameElements = DOMUtils.$$('.company-name');
    companyNameElements.forEach(element => {
        element.textContent = companyInfo.name;
    });
}

// تحميل البيانات المخزنة
async function loadStoredData() {
    updateLoadingMessage('جاري تحميل البيانات المحفوظة...');
    
    // تحميل المنتجات
    const products = db.getProducts();
    console.log(`📦 تم تحميل ${products.length} منتج`);
    
    // تحميل العملاء
    const customers = db.getCustomers();
    console.log(`👥 تم تحميل ${customers.length} عميل`);
    
    // تحميل الموردين
    const suppliers = db.getSuppliers();
    console.log(`🚚 تم تحميل ${suppliers.length} مورد`);
    
    // تحميل المبيعات
    const sales = db.getSales();
    console.log(`💰 تم تحميل ${sales.length} عملية بيع`);
    
    await Utils.delay(300);
}

// تهيئة المكونات
async function initializeComponents() {
    updateLoadingMessage('جاري تهيئة مكونات التطبيق...');
    
    // التحقق من وجود المكونات الأساسية
    const requiredComponents = ['auth', 'app', 'db'];
    for (const component of requiredComponents) {
        if (!window[component]) {
            throw new Error(`المكون المطلوب غير متوفر: ${component}`);
        }
    }
    
    // تهيئة نظام الإشعارات
    if (window.notificationSystem) {
        await window.notificationSystem.init();
    }
    
    // تهيئة نظام الطباعة
    if (window.printSystem) {
        await window.printSystem.init();
    }
    
    // تهيئة نظام النسخ الاحتياطي
    if (window.backupSystem) {
        await window.backupSystem.init();
    }
    
    await Utils.delay(300);
}

// تحميل حالة التطبيق المحفوظة
function loadAppState() {
    const savedState = StorageUtils.get('app_state');
    if (savedState) {
        // استعادة حالة القائمة الجانبية
        if (savedState.sidebarCollapsed && window.app) {
            window.app.sidebarCollapsed = savedState.sidebarCollapsed;
            if (savedState.sidebarCollapsed) {
                DOMUtils.addClass('#sidebar', 'collapsed');
                DOMUtils.addClass('#main-content', 'expanded');
            }
        }
        
        // استعادة الصفحة الحالية
        if (savedState.currentPage && window.app) {
            // سيتم تحديد الصفحة بعد تسجيل الدخول
            window.app.currentPage = savedState.currentPage;
        }
    }
}

// إظهار شاشة الخطأ
function showErrorScreen(error) {
    hideLoadingScreen();
    
    const errorHTML = `
        <div class="error-screen">
            <div class="error-container">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2>حدث خطأ في تحميل التطبيق</h2>
                <p class="error-message">${error.message || 'خطأ غير معروف'}</p>
                <div class="error-actions">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        إعادة تحميل الصفحة
                    </button>
                    <button class="btn btn-secondary" onclick="clearAppData()">
                        <i class="fas fa-trash"></i>
                        مسح البيانات وإعادة التشغيل
                    </button>
                </div>
                <details class="error-details">
                    <summary>تفاصيل الخطأ التقنية</summary>
                    <pre>${error.stack || error.toString()}</pre>
                </details>
            </div>
        </div>
    `;
    
    document.body.innerHTML = errorHTML;
}

// مسح بيانات التطبيق
function clearAppData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        try {
            localStorage.clear();
            sessionStorage.clear();
            location.reload();
        } catch (error) {
            alert('فشل في مسح البيانات: ' + error.message);
        }
    }
}

// إطلاق حدث جاهزية التطبيق
function dispatchAppReadyEvent() {
    const event = new CustomEvent('appReady', {
        detail: {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            components: Object.keys(window).filter(key => 
                typeof window[key] === 'object' && 
                window[key] !== null && 
                key.startsWith('cm_') || 
                ['app', 'auth', 'db'].includes(key)
            )
        }
    });
    
    document.dispatchEvent(event);
}

// مستمع حدث جاهزية التطبيق
document.addEventListener('appReady', function(event) {
    console.log('🎉 التطبيق جاهز للاستخدام:', event.detail);
    
    // تشغيل المهام التي تحتاج التطبيق ليكون جاهزاً
    runPostInitTasks();
});

// تشغيل المهام بعد التهيئة
function runPostInitTasks() {
    // فحص التحديثات
    checkForUpdates();
    
    // تشغيل النسخ الاحتياطي التلقائي
    startAutoBackup();
    
    // تحسين الأداء
    optimizePerformance();
    
    // إعداد معالجات الأخطاء
    setupErrorHandlers();
}

// فحص التحديثات
function checkForUpdates() {
    // يمكن إضافة منطق فحص التحديثات هنا
    console.log('🔍 فحص التحديثات...');
}

// تشغيل النسخ الاحتياطي التلقائي
function startAutoBackup() {
    const settings = db.getSettings();
    if (settings.system?.autoBackup) {
        const interval = (settings.system.backupInterval || 7) * 24 * 60 * 60 * 1000; // بالأيام
        
        setInterval(() => {
            if (window.backupSystem && typeof window.backupSystem.autoBackup === 'function') {
                window.backupSystem.autoBackup();
            }
        }, interval);
        
        console.log('💾 تم تفعيل النسخ الاحتياطي التلقائي');
    }
}

// تحسين الأداء
function optimizePerformance() {
    // تحسين الصور
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.loading = 'lazy';
    });
    
    // تحسين الخطوط
    if ('fonts' in document) {
        document.fonts.ready.then(() => {
            console.log('🔤 تم تحميل الخطوط بنجاح');
        });
    }
    
    // تنظيف الذاكرة
    if (window.gc && typeof window.gc === 'function') {
        setTimeout(() => {
            window.gc();
        }, 5000);
    }
}

// إعداد معالجات الأخطاء
function setupErrorHandlers() {
    // معالج الأخطاء العام
    window.addEventListener('error', function(event) {
        console.error('❌ خطأ في التطبيق:', event.error);
        
        // إرسال تقرير الخطأ (يمكن إضافة منطق الإرسال هنا)
        logError({
            message: event.error?.message || 'خطأ غير معروف',
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack,
            timestamp: new Date().toISOString()
        });
    });
    
    // معالج الأخطاء غير المعالجة
    window.addEventListener('unhandledrejection', function(event) {
        console.error('❌ خطأ غير معالج:', event.reason);
        
        logError({
            message: 'Unhandled Promise Rejection',
            reason: event.reason?.toString() || 'Unknown reason',
            timestamp: new Date().toISOString()
        });
    });
}

// تسجيل الأخطاء
function logError(errorInfo) {
    // حفظ الخطأ في التخزين المحلي
    const errors = StorageUtils.get('app_errors', []);
    errors.unshift(errorInfo);
    
    // الاحتفاظ بآخر 100 خطأ فقط
    if (errors.length > 100) {
        errors.splice(100);
    }
    
    StorageUtils.set('app_errors', errors);
}

// وظائف مساعدة للتطوير
if (process?.env?.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
    // إضافة وظائف التطوير
    window.devTools = {
        clearData: () => {
            localStorage.clear();
            sessionStorage.clear();
            console.log('🧹 تم مسح جميع البيانات');
        },
        
        exportData: () => {
            const data = db.exportData();
            Utils.downloadFile(data, `cybernet-magasin-backup-${DateUtils.getCurrentDate()}.json`);
            console.log('📤 تم تصدير البيانات');
        },
        
        getStats: () => {
            return db.getQuickStats();
        },
        
        getErrors: () => {
            return StorageUtils.get('app_errors', []);
        }
    };
    
    console.log('🛠️ أدوات التطوير متاحة في window.devTools');
}

// تصدير الوظائف للاستخدام العام
window.initializeApp = initializeApp;
window.clearAppData = clearAppData;
