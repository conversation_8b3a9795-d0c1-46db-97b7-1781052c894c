/**
 * Cybernet Magasin - التطبيق الرئيسي
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class CybernetMagasinApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.sidebarCollapsed = false;
        this.currentTheme = 'light';
        this.notifications = [];
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.startAutoSave();
        this.checkNotifications();
        this.ensureSampleData();
    }

    // تحميل الإعدادات
    loadSettings() {
        const settings = db.getSettings();
        this.currentTheme = settings.system?.theme || 'light';
        this.applySavedTheme();
    }

    // تطبيق الثيم المحفوظ
    applySavedTheme() {
        document.body.className = `${this.currentTheme}-theme`;
        this.updateThemeIcon();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تبديل القائمة الجانبية
        window.toggleSidebar = () => {
            this.toggleSidebar();
        };

        // تبديل الثيم
        window.toggleTheme = () => {
            this.toggleTheme();
        };

        // إظهار الإشعارات
        window.showNotifications = () => {
            this.showNotifications();
        };

        // تبديل قائمة المستخدم
        window.toggleUserMenu = () => {
            this.toggleUserMenu();
        };

        // إظهار الإعدادات
        window.showSettings = () => {
            this.showPage('settings');
        };

        // إظهار الصفحات
        window.showPage = (pageName) => {
            this.showPage(pageName);
        };

        // إغلاق النوافذ المنبثقة
        window.closeModal = (modalId) => {
            this.closeModal(modalId);
        };

        // إظهار نافذة التأكيد
        window.showConfirm = (title, message, callback) => {
            this.showConfirm(title, message, callback);
        };

        // إظهار الإشعارات
        window.showNotification = (message, type = 'info', duration = 3000) => {
            this.showNotification(message, type, duration);
        };

        // مستمع النقر خارج القوائم المنبثقة
        document.addEventListener('click', (e) => {
            this.handleOutsideClick(e);
        });

        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // مستمع إغلاق النافذة
        window.addEventListener('beforeunload', (e) => {
            this.handleBeforeUnload(e);
        });
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + اختصارات
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.showPage('dashboard');
                        break;
                    case '2':
                        e.preventDefault();
                        this.showPage('sales');
                        break;
                    case '3':
                        e.preventDefault();
                        this.showPage('products');
                        break;
                    case '4':
                        e.preventDefault();
                        this.showPage('customers');
                        break;
                    case 's':
                        e.preventDefault();
                        this.saveData();
                        break;
                    case 'p':
                        e.preventDefault();
                        window.print();
                        break;
                }
            }

            // مفتاح Escape
            if (e.key === 'Escape') {
                this.closeAllModals();
                this.closeAllDropdowns();
            }

            // F11 للوضع الكامل
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
        });
    }

    // بدء الحفظ التلقائي
    startAutoSave() {
        setInterval(() => {
            this.autoSave();
        }, 30000); // كل 30 ثانية
    }

    // فحص الإشعارات
    checkNotifications() {
        this.checkLowStock();
        this.checkOverduePayments();
        this.updateNotificationCount();
        
        // فحص دوري كل 5 دقائق
        setInterval(() => {
            this.checkLowStock();
            this.checkOverduePayments();
            this.updateNotificationCount();
        }, 5 * 60 * 1000);
    }

    // تبديل القائمة الجانبية
    toggleSidebar() {
        const sidebar = DOMUtils.$('#sidebar');
        const mainContent = DOMUtils.$('#main-content');
        
        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            DOMUtils.addClass(sidebar, 'collapsed');
            DOMUtils.addClass(mainContent, 'expanded');
        } else {
            DOMUtils.removeClass(sidebar, 'collapsed');
            DOMUtils.removeClass(mainContent, 'expanded');
        }
        
        // حفظ حالة القائمة الجانبية
        StorageUtils.set('sidebar_collapsed', this.sidebarCollapsed);
    }

    // تبديل الثيم
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.body.className = `${this.currentTheme}-theme`;
        this.updateThemeIcon();
        this.saveThemePreference();
    }

    // تحديث أيقونة الثيم
    updateThemeIcon() {
        const themeIcon = DOMUtils.$('.theme-toggle i');
        if (themeIcon) {
            themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // حفظ تفضيل الثيم
    saveThemePreference() {
        const settings = db.getSettings();
        settings.system = settings.system || {};
        settings.system.theme = this.currentTheme;
        db.saveSettings(settings);
    }

    // إظهار الصفحات
    showPage(pageName) {
        // التحقق من المصادقة
        if (!auth.isAuthenticated()) {
            auth.showLogin();
            return;
        }

        // إخفاء جميع الصفحات
        const pages = DOMUtils.$$('.page');
        pages.forEach(page => {
            DOMUtils.removeClass(page, 'active');
        });

        // إزالة الفئة النشطة من جميع روابط التنقل
        const navLinks = DOMUtils.$$('.nav-item');
        navLinks.forEach(item => {
            DOMUtils.removeClass(item, 'active');
        });

        // إظهار الصفحة المطلوبة
        const targetPage = DOMUtils.$(`#${pageName}-page`);
        if (targetPage) {
            DOMUtils.addClass(targetPage, 'active');
            this.currentPage = pageName;
            
            // تحديث عنوان الصفحة
            this.updatePageTitle(pageName);
            
            // تفعيل رابط التنقل المناسب
            const navItem = DOMUtils.$(`[onclick="showPage('${pageName}')"]`)?.closest('.nav-item');
            if (navItem) {
                DOMUtils.addClass(navItem, 'active');
            }
            
            // تحديث محتوى الصفحة
            this.loadPageContent(pageName);
        }

        // إغلاق القائمة الجانبية في الشاشات الصغيرة
        if (window.innerWidth <= 768) {
            DOMUtils.removeClass('#sidebar', 'show');
        }
    }

    // تحديث عنوان الصفحة
    updatePageTitle(pageName) {
        const titles = {
            dashboard: 'لوحة المعلومات',
            sales: 'نقطة البيع',
            products: 'إدارة المنتجات',
            customers: 'إدارة العملاء',
            suppliers: 'إدارة الموردين',
            purchases: 'المشتريات',
            reports: 'التقارير',
            settings: 'الإعدادات'
        };
        
        const pageTitle = DOMUtils.$('.page-title');
        if (pageTitle) {
            pageTitle.textContent = titles[pageName] || 'Cybernet Magasin';
        }
        
        document.title = `${titles[pageName] || 'Cybernet Magasin'} - نظام إدارة نقاط البيع`;
    }

    // تحميل محتوى الصفحة
    loadPageContent(pageName) {
        switch (pageName) {
            case 'dashboard':
                if (window.dashboard && typeof window.dashboard.render === 'function') {
                    window.dashboard.render();
                }
                break;
            case 'sales':
                if (window.salesSystem && typeof window.salesSystem.render === 'function') {
                    window.salesSystem.render();
                }
                break;
            case 'products':
                if (window.productsManager && typeof window.productsManager.render === 'function') {
                    window.productsManager.render();
                }
                break;
            case 'customers':
                if (window.customersManager && typeof window.customersManager.render === 'function') {
                    window.customersManager.render();
                }
                break;
            case 'suppliers':
                if (window.suppliersManager && typeof window.suppliersManager.render === 'function') {
                    window.suppliersManager.render();
                }
                break;
            case 'purchases':
                if (window.purchasesManager && typeof window.purchasesManager.render === 'function') {
                    window.purchasesManager.render();
                }
                break;
            case 'reports':
                if (window.reportsManager && typeof window.reportsManager.render === 'function') {
                    window.reportsManager.render();
                }
                break;
            case 'settings':
                if (window.settingsManager && typeof window.settingsManager.render === 'function') {
                    window.settingsManager.render();
                }
                break;
        }
    }

    // تبديل قائمة المستخدم
    toggleUserMenu() {
        const dropdown = DOMUtils.$('.user-dropdown');
        DOMUtils.toggleClass(dropdown, 'show');
    }

    // إظهار الإشعارات
    showNotifications() {
        const modal = DOMUtils.$('#notifications-modal');
        const notificationsList = DOMUtils.$('#notifications-list');
        
        // تحديث قائمة الإشعارات
        this.updateNotificationsList(notificationsList);
        
        // إظهار النافذة المنبثقة
        DOMUtils.addClass(modal, 'show');
    }

    // تحديث قائمة الإشعارات
    updateNotificationsList(container) {
        if (!container) return;
        
        if (this.notifications.length === 0) {
            container.innerHTML = '<p class="text-center text-secondary">لا توجد إشعارات جديدة</p>';
            return;
        }
        
        const notificationsHTML = this.notifications.map(notification => `
            <div class="notification-item ${notification.type}">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                    <small>${DateUtils.formatDate(notification.timestamp, 'dd/mm/yyyy hh:mm')}</small>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = notificationsHTML;
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle'
        };
        return icons[type] || 'fas fa-bell';
    }

    // إظهار نافذة التأكيد
    showConfirm(title, message, callback) {
        const modal = DOMUtils.$('#confirm-modal');
        const titleElement = DOMUtils.$('#confirm-title');
        const messageElement = DOMUtils.$('#confirm-message');
        const confirmBtn = DOMUtils.$('#confirm-btn');
        
        if (titleElement) titleElement.textContent = title;
        if (messageElement) messageElement.textContent = message;
        
        // إزالة مستمعي الأحداث السابقين
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // إضافة مستمع الحدث الجديد
        newConfirmBtn.addEventListener('click', () => {
            this.closeModal('confirm-modal');
            if (typeof callback === 'function') {
                callback();
            }
        });
        
        DOMUtils.addClass(modal, 'show');
    }

    // إغلاق النافذة المنبثقة
    closeModal(modalId) {
        const modal = DOMUtils.$(`#${modalId}`);
        DOMUtils.removeClass(modal, 'show');
    }

    // إغلاق جميع النوافذ المنبثقة
    closeAllModals() {
        const modals = DOMUtils.$$('.modal');
        modals.forEach(modal => {
            DOMUtils.removeClass(modal, 'show');
        });
    }

    // إغلاق جميع القوائم المنسدلة
    closeAllDropdowns() {
        const dropdowns = DOMUtils.$$('.dropdown, .user-dropdown');
        dropdowns.forEach(dropdown => {
            DOMUtils.removeClass(dropdown, 'show');
        });
    }

    // معالجة النقر خارج العناصر
    handleOutsideClick(e) {
        // إغلاق قائمة المستخدم
        const userMenu = DOMUtils.$('.user-menu');
        const userDropdown = DOMUtils.$('.user-dropdown');
        if (userMenu && !userMenu.contains(e.target)) {
            DOMUtils.removeClass(userDropdown, 'show');
        }
    }

    // معالجة تغيير حجم النافذة
    handleResize() {
        // إغلاق القائمة الجانبية في الشاشات الصغيرة
        if (window.innerWidth <= 768) {
            DOMUtils.removeClass('#sidebar', 'show');
        }
    }

    // معالجة إغلاق النافذة
    handleBeforeUnload(e) {
        // حفظ البيانات قبل الإغلاق
        this.saveData();
    }

    // فحص المخزون المنخفض
    checkLowStock() {
        const products = db.getProducts();
        const settings = db.getSettings();
        const lowStockAlert = settings.system?.lowStockAlert || 10;
        
        const lowStockProducts = products.filter(product => 
            product.quantity <= lowStockAlert && product.active
        );
        
        if (lowStockProducts.length > 0) {
            const notification = {
                id: 'low-stock',
                type: 'warning',
                title: 'تنبيه مخزون منخفض',
                message: `يوجد ${lowStockProducts.length} منتج بمخزون منخفض`,
                timestamp: new Date().toISOString()
            };
            
            this.addNotification(notification);
        }
    }

    // فحص المدفوعات المتأخرة
    checkOverduePayments() {
        const customers = db.getCustomers();
        const overdueCustomers = customers.filter(customer => 
            customer.balance > 0 && !customer.isDefault
        );
        
        if (overdueCustomers.length > 0) {
            const totalDebt = overdueCustomers.reduce((sum, customer) => sum + customer.balance, 0);
            
            const notification = {
                id: 'overdue-payments',
                type: 'danger',
                title: 'ديون مستحقة',
                message: `يوجد ${overdueCustomers.length} عميل بإجمالي ديون ${NumberUtils.formatCurrency(totalDebt)}`,
                timestamp: new Date().toISOString()
            };
            
            this.addNotification(notification);
        }
    }

    // إضافة إشعار
    addNotification(notification) {
        // التحقق من عدم وجود الإشعار مسبقاً
        const existingIndex = this.notifications.findIndex(n => n.id === notification.id);
        if (existingIndex !== -1) {
            this.notifications[existingIndex] = notification;
        } else {
            this.notifications.unshift(notification);
        }
        
        // الاحتفاظ بآخر 50 إشعار فقط
        if (this.notifications.length > 50) {
            this.notifications = this.notifications.slice(0, 50);
        }
        
        this.updateNotificationCount();
    }

    // تحديث عدد الإشعارات
    updateNotificationCount() {
        const countElement = DOMUtils.$('.notification-count');
        if (countElement) {
            const count = this.notifications.length;
            countElement.textContent = count;
            countElement.style.display = count > 0 ? 'block' : 'none';
        }
    }

    // إظهار إشعار مؤقت
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `toast toast-${type}`;
        notification.innerHTML = `
            <div class="toast-content">
                <i class="${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // إخفاء الإشعار
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }

    // تبديل الوضع الكامل
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    // الحفظ التلقائي
    autoSave() {
        this.saveData();
    }

    // حفظ البيانات
    saveData() {
        // تحديث الجلسة
        auth.refreshSession();
        
        // حفظ حالة التطبيق
        const appState = {
            currentPage: this.currentPage,
            sidebarCollapsed: this.sidebarCollapsed,
            theme: this.currentTheme,
            lastSaved: new Date().toISOString()
        };
        
        StorageUtils.set('app_state', appState);
    }

    // تحديث واجهة المستخدم
    updateUI() {
        // تحديث الإحصائيات
        if (this.currentPage === 'dashboard' && window.dashboard) {
            window.dashboard.updateStats();
        }
        
        // تحديث الإشعارات
        this.checkNotifications();
    }

    // الحصول على حالة التطبيق
    getAppState() {
        return {
            currentPage: this.currentPage,
            sidebarCollapsed: this.sidebarCollapsed,
            theme: this.currentTheme,
            isAuthenticated: auth.isAuthenticated(),
            notifications: this.notifications.length
        };
    }

    // التأكد من وجود بيانات تجريبية
    ensureSampleData() {
        const sales = db.getSales();
        const purchases = db.getPurchases();
        const products = db.getProducts();

        // إذا لم توجد بيانات، أعد إنشاء البيانات التجريبية
        if (sales.length === 0 && purchases.length === 0 && products.length === 0) {
            console.log('إنشاء بيانات تجريبية...');
            db.createSampleData();
            this.showNotification('تم إنشاء بيانات تجريبية للنظام', 'info');
        }
    }
}

// إنشاء مثيل من التطبيق
const app = new CybernetMagasinApp();

// تصدير التطبيق للاستخدام العام
window.app = app;
