/* 
 * Cyber<PERSON> Magasin - نظام إدارة نقاط البيع
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

/* إعدادات عامة */
:root {
    /* الألوان الأساسية */
    --primary-color: #730C02;
    --secondary-color: #F2490C;
    --accent-color: #1DDF2;
    --info-color: #0F8DBF;
    --dark-color: #0A3B59;
    
    /* ألوان إضافية */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    /* ألوان الخلفية */
    --bg-primary: #f5f7fa;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    
    /* الظلال */
    --shadow-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --shadow-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    --shadow-hover: 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff;
    --shadow-pressed: inset 4px 4px 8px #d1d9e6, inset -4px -4px 8px #ffffff;
    
    /* الخطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الحدود */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* إعادة تعيين الأنماط */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--bg-primary);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* الفقرات */
p {
    margin-bottom: var(--spacing-md);
}

/* القوائم */
ul, ol {
    margin-bottom: var(--spacing-md);
    padding-right: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-light);
    background: var(--bg-secondary);
    color: var(--gray-800);
    min-height: 44px;
}

.btn:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.btn:active {
    box-shadow: var(--shadow-pressed);
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-light);
}

/* أنواع الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-secondary {
    background: var(--gray-500);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--gray-900);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

/* البطاقات */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0;
}

.card-body {
    padding: 0;
}

.card-footer {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* النماذج */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    box-shadow: var(--shadow-inset);
    transition: var(--transition-normal);
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-inset), 0 0 0 3px rgba(115, 12, 2, 0.1);
}

.form-control:invalid {
    border-color: var(--danger-color);
}

/* مجموعة الإدخال */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .form-control {
    padding-left: 44px;
}

.input-group .toggle-password {
    position: absolute;
    left: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.input-group .toggle-password:hover {
    color: var(--primary-color);
    background: var(--gray-100);
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-100);
    font-weight: 600;
    color: var(--gray-800);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* الأدوات المساعدة */
.hidden { display: none !important; }
.visible { display: block !important; }
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.m-0 { margin: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }

.p-0 { padding: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }

/* رسائل الخطأ والنجاح */
.error-message {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: none;
}

.success-message {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: none;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(15, 141, 191, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: var(--transition-slow);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner h3 {
    color: white;
    font-weight: 400;
    margin: 0;
}

/* شاشة تسجيل الدخول */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: var(--transition-slow);
}

.login-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-2xl);
    width: 100%;
    max-width: 400px;
    text-align: center;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-container .logo {
    margin-bottom: var(--spacing-2xl);
}

.login-container .logo i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: block;
}

.login-container .logo h1 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-3xl);
}

.login-container .logo p {
    color: var(--gray-600);
    margin-bottom: 0;
    font-size: var(--font-size-base);
}

.login-form {
    text-align: right;
}

.login-form h2 {
    text-align: center;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-size-2xl);
}

.login-form .form-group {
    margin-bottom: var(--spacing-xl);
}

.login-form label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    text-align: right;
}

.login-form .btn {
    width: 100%;
    font-size: var(--font-size-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.login-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    text-align: center;
}

.login-footer p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.login-footer strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* التطبيق الرئيسي */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: var(--transition-normal);
}

/* الشريط العلوي */
.top-bar {
    background: var(--bg-secondary);
    box-shadow: var(--shadow-light);
    padding: 0 var(--spacing-lg);
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: var(--transition-normal);
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.page-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.theme-toggle button,
.notification-btn,
.user-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--gray-600);
}

.theme-toggle button:hover,
.notification-btn:hover,
.user-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.notifications {
    position: relative;
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    min-width: 180px;
    padding: var(--spacing-sm) 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
    z-index: 1000;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition-fast);
}

.user-dropdown a:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

/* القائمة الجانبية */
.sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--bg-secondary);
    box-shadow: var(--shadow-light);
    transform: translateX(0);
    transition: var(--transition-normal);
    z-index: 90;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar.collapsed .sidebar-nav span {
    opacity: 0;
    visibility: hidden;
}

.sidebar.collapsed .logo-mini span {
    opacity: 0;
    visibility: hidden;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.logo-mini {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
}

.logo-mini i {
    font-size: var(--font-size-2xl);
}

.logo-mini span {
    transition: var(--transition-normal);
}

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    margin-left: var(--spacing-lg);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--shadow-hover);
}

.nav-link i {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.nav-link span {
    font-weight: 500;
    transition: var(--transition-normal);
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 70px;
    margin-right: 280px;
    padding: var(--spacing-xl);
    min-height: calc(100vh - 70px);
    transition: var(--transition-normal);
}

.main-content.expanded {
    margin-right: 70px;
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideUp 0.3s ease-out;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-800);
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--gray-100);
    color: var(--danger-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
        z-index: 1000;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
    }

    .top-bar {
        padding: 0 var(--spacing-md);
    }

    .login-container {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
    }

    .modal-content {
        margin: var(--spacing-md);
        width: calc(100% - 2rem);
    }

    .row {
        margin: 0;
    }

    .col {
        padding: 0;
        margin-bottom: var(--spacing-md);
    }

    .col-1, .col-2, .col-3, .col-4, .col-6, .col-8, .col-9 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .top-bar-left .page-title {
        display: none;
    }

    .user-btn span {
        display: none;
    }

    .btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .card {
        padding: var(--spacing-md);
    }
}

/* تحسينات الأداء */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.sidebar,
.main-content,
.top-bar {
    will-change: transform;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* لوحة المعلومات */
.dashboard-container {
    padding: var(--spacing-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stats-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-4px);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stats-card.primary::before {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.stats-card.success::before {
    background: var(--success-color);
}

.stats-card.info::before {
    background: var(--info-color);
}

.stats-card.warning::before {
    background: var(--warning-color);
}

.stats-card .stats-icon {
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(115, 12, 2, 0.1);
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
}

.stats-card.success .stats-icon {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.stats-card.info .stats-icon {
    background: rgba(15, 141, 191, 0.1);
    color: var(--info-color);
}

.stats-card.warning .stats-icon {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stats-card .stats-content {
    text-align: right;
    padding-right: 80px;
}

.stats-card .stats-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.stats-card .stats-content p {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-sm);
}

.stats-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stats-change.positive {
    color: var(--success-color);
}

.stats-change.negative {
    color: var(--danger-color);
}

.stats-detail,
.stats-alert,
.stats-debt {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.stats-alert {
    color: var(--warning-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stats-debt {
    color: var(--danger-color);
}

/* الرسوم البيانية */
.charts-section {
    margin-bottom: var(--spacing-2xl);
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.simple-chart {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 200px;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) 0;
}

.chart-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.bar {
    width: 100%;
    max-width: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    position: relative;
    transition: var(--transition-normal);
    min-height: 10px;
}

.bar:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.bar-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    z-index: 10;
}

.bar:hover .bar-tooltip {
    opacity: 1;
    visibility: visible;
}

.bar-label {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    text-align: center;
}

/* أفضل المنتجات */
.top-products-list {
    max-height: 300px;
    overflow-y: auto;
}

.product-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    margin-bottom: var(--spacing-sm);
}

.product-item:hover {
    background: var(--gray-100);
}

.product-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.product-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.product-info p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* التنبيهات */
.alerts-section {
    margin-bottom: var(--spacing-2xl);
}

.stock-alerts,
.debt-alerts {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    background: var(--gray-50);
    transition: var(--transition-fast);
}

.alert-item:hover {
    background: var(--gray-100);
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.alert-content h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.alert-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.alert-more {
    text-align: center;
    padding: var(--spacing-md);
}

.alert-more a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.alert-more a:hover {
    text-decoration: underline;
}

/* الأنشطة الأخيرة */
.activities-section {
    margin-bottom: var(--spacing-2xl);
}

.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    font-size: var(--font-size-lg);
}

.activity-content {
    flex: 1;
}

.activity-content h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.activity-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.activity-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* الإشعارات المنبثقة */
.toast {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-md);
    z-index: 3000;
    transform: translateX(-100%);
    opacity: 0;
    transition: var(--transition-normal);
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toast.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast.toast-warning {
    border-left: 4px solid var(--warning-color);
}

.toast.toast-danger {
    border-left: 4px solid var(--danger-color);
}

.toast.toast-info {
    border-left: 4px solid var(--info-color);
}

/* نظام المبيعات */
.sales-container {
    padding: var(--spacing-lg);
    height: calc(100vh - 70px);
    display: flex;
    flex-direction: column;
}

.sales-toolbar {
    margin-bottom: var(--spacing-lg);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.search-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 300px;
    position: relative;
    display: flex;
}

.search-box input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    font-size: var(--font-size-base);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn:hover {
    background: var(--secondary-color);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.sales-content {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    overflow: hidden;
}

/* قائمة المنتجات */
.products-panel {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h3 {
    margin: 0;
    color: var(--gray-800);
}

.category-filter select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: var(--bg-secondary);
}

.products-grid {
    flex: 1;
    padding: var(--spacing-lg);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    overflow-y: auto;
}

.product-card {
    background: var(--bg-secondary);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.product-image {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    border-radius: var(--border-radius);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image i {
    font-size: var(--font-size-2xl);
    color: var(--gray-400);
}

.product-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.product-price {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 var(--spacing-xs) 0;
}

.product-stock {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

.no-products {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-2xl);
}

/* سلة المشتريات */
.cart-panel {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.customer-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.customer-section label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--gray-700);
}

.customer-section select {
    width: calc(100% - 50px);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: var(--bg-secondary);
}

.customer-section button {
    width: 40px;
    margin-right: var(--spacing-xs);
}

.cart-items {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 300px;
}

.empty-cart {
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-2xl);
}

.cart-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-secondary);
}

.item-info h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    color: var(--gray-900);
}

.item-info p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.quantity-controls button {
    background: var(--gray-100);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.quantity-controls button:hover {
    background: var(--gray-200);
}

.quantity-controls span {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    min-width: 40px;
    text-align: center;
}

.item-total {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 80px;
    text-align: center;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.remove-btn:hover {
    background: #c82333;
}

/* الإجماليات */
.cart-totals {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    border-bottom: 1px solid var(--gray-200);
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.total-row:last-child {
    margin-bottom: 0;
}

.total-row.grand-total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    border-top: 2px solid var(--primary-color);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.discount-input {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.discount-input input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

/* طريقة الدفع */
.payment-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.payment-section label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.payment-methods {
    display: flex;
    gap: var(--spacing-lg);
}

.payment-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.payment-option input[type="radio"] {
    margin: 0;
}

/* الملاحظات */
.notes-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.notes-section label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--gray-700);
}

.notes-section textarea {
    width: 100%;
    min-height: 60px;
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    resize: vertical;
    font-family: var(--font-family);
}

/* أزرار العمليات */
.action-buttons {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-buttons .btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

/* النوافذ المنبثقة الكبيرة */
.modal-content.large {
    max-width: 800px;
    width: 95%;
}

.products-table-container {
    max-height: 400px;
    overflow-y: auto;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .sales-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }

    .cart-panel {
        max-height: 50vh;
    }
}

@media (max-width: 768px) {
    .search-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .quick-actions {
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .cart-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .item-controls {
        justify-content: space-between;
    }
}

/* إدارة المنتجات */
.products-container {
    padding: var(--spacing-lg);
}

.products-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.toolbar-left h2 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--gray-900);
}

.products-count {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.toolbar-right {
    display: flex;
    gap: var(--spacing-sm);
}

.products-filters {
    display: flex;
    align-items: end;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 150px;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: var(--bg-secondary);
    font-size: var(--font-size-sm);
}

.search-box {
    display: flex;
    min-width: 250px;
}

.search-box input {
    flex: 1;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    border-right: none;
}

.search-box button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    cursor: pointer;
}

.sort-order-btn {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    margin-right: var(--spacing-xs);
    transition: var(--transition-fast);
}

.sort-order-btn:hover {
    background: var(--gray-200);
}

.filter-actions {
    display: flex;
    align-items: end;
}

.products-table-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.products-table {
    width: 100%;
    margin: 0;
}

.products-table th {
    background: var(--gray-100);
    padding: var(--spacing-md);
    font-weight: 600;
    color: var(--gray-800);
    border-bottom: 2px solid var(--gray-200);
}

.products-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.product-row.inactive {
    opacity: 0.6;
    background: var(--gray-50);
}

.product-image-cell {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
}

.product-image-cell img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-cell i {
    font-size: var(--font-size-xl);
    color: var(--gray-400);
}

.product-name-cell strong {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--gray-900);
}

.product-name-cell small {
    color: var(--gray-600);
    font-size: var(--font-size-xs);
}

.price-cell {
    font-weight: 600;
    color: var(--primary-color);
}

.quantity-cell .quantity {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    background: var(--success-color);
    color: white;
}

.quantity-cell .quantity.low-stock {
    background: var(--warning-color);
    color: var(--gray-900);
}

.quantity-cell .quantity.out-of-stock {
    background: var(--danger-color);
    color: white;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-badge.active {
    background: var(--success-color);
    color: white;
}

.status-badge.inactive {
    background: var(--gray-400);
    color: white;
}

.actions-cell {
    width: 200px;
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.action-buttons .btn {
    padding: var(--spacing-xs);
    min-width: 32px;
}

.no-data {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--gray-500);
}

.no-data i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    display: block;
}

/* التنقل بين الصفحات */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.pagination {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.page-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--gray-300);
    background: var(--bg-secondary);
    color: var(--gray-700);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    min-width: 36px;
    text-align: center;
}

.page-btn:hover {
    background: var(--gray-100);
}

.page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-dots {
    padding: var(--spacing-xs);
    color: var(--gray-500);
}

/* الإجراءات المجمعة */
.bulk-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-lg);
    z-index: 100;
    border: 2px solid var(--primary-color);
}

.bulk-actions-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.bulk-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

/* نموذج المنتج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-row .form-group {
    margin-bottom: 0;
}

.image-upload {
    position: relative;
}

.image-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.image-preview {
    width: 150px;
    height: 150px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    overflow: hidden;
}

.image-preview:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview i {
    font-size: var(--font-size-2xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-xs);
}

.image-preview span {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* تفاصيل المنتج */
.product-details {
    max-width: 600px;
}

.product-header {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.product-detail-image {
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

.product-detail-placeholder {
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-detail-placeholder i {
    font-size: 3rem;
    color: var(--gray-400);
}

.product-basic-info h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--gray-900);
}

.product-category {
    color: var(--gray-600);
    margin: 0 0 var(--spacing-sm) 0;
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.product-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
}

.info-item span {
    font-weight: 500;
    color: var(--gray-900);
}

.product-description {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.product-description label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.product-description p {
    color: var(--gray-800);
    line-height: 1.6;
    margin: 0;
}

/* رفع الملفات */
.file-upload {
    margin: var(--spacing-lg) 0;
}

.file-upload input[type="file"] {
    display: none;
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
}

.file-upload-label:hover {
    background: var(--secondary-color);
}

.import-options {
    margin-top: var(--spacing-lg);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .products-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .product-info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .products-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .toolbar-right {
        justify-content: center;
    }

    .products-table-container {
        overflow-x: auto;
    }

    .products-table {
        min-width: 800px;
    }

    .bulk-actions {
        left: 10px;
        right: 10px;
        transform: none;
    }

    .bulk-actions-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .product-header {
        flex-direction: column;
        text-align: center;
    }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .top-bar,
    .btn,
    .modal {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .page {
        display: block !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
