/**
 * Cybernet Magasin - إدارة الموردين
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class SuppliersManager {
    constructor() {
        this.suppliers = [];
        this.filteredSuppliers = [];
        this.currentSupplier = null;
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.init();
    }

    // تهيئة مدير الموردين
    init() {
        this.loadData();
    }

    // تحميل البيانات
    loadData() {
        this.suppliers = db.getSuppliers();
        this.filteredSuppliers = [...this.suppliers];
    }

    // عرض واجهة إدارة الموردين
    render() {
        const container = DOMUtils.$('#suppliers-page');
        if (!container) return;

        this.loadData();

        container.innerHTML = `
            <div class="suppliers-container">
                <!-- شريط الأدوات -->
                <div class="suppliers-toolbar">
                    <div class="toolbar-left">
                        <h2>إدارة الموردين</h2>
                        <span class="suppliers-count">${this.suppliers.length} مورد</span>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="suppliersManager.showAddSupplierModal()">
                            <i class="fas fa-plus"></i>
                            إضافة مورد جديد
                        </button>
                        <button class="btn btn-info" onclick="suppliersManager.exportSuppliers()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="btn btn-warning" onclick="suppliersManager.showPayablesReport()">
                            <i class="fas fa-file-invoice-dollar"></i>
                            تقرير المستحقات
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="suppliers-filters">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <div class="search-box">
                            <input type="text" id="suppliers-search" placeholder="ابحث بالاسم، الهاتف، أو العنوان...">
                            <button onclick="suppliersManager.searchSuppliers()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" onchange="suppliersManager.filterByStatus()">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="payable">مستحق الدفع</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>ترتيب حسب:</label>
                        <select id="sort-by" onchange="suppliersManager.sortSuppliers()">
                            <option value="name">الاسم</option>
                            <option value="balance">الرصيد</option>
                            <option value="createdAt">تاريخ الإضافة</option>
                        </select>
                        <button class="sort-order-btn" onclick="suppliersManager.toggleSortOrder()">
                            <i class="fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}"></i>
                        </button>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-sm btn-secondary" onclick="suppliersManager.clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>

                <!-- جدول الموردين -->
                <div class="suppliers-table-container">
                    <table class="table suppliers-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" onchange="suppliersManager.toggleSelectAll()">
                                </th>
                                <th>اسم المورد</th>
                                <th>الشركة</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table-body">
                            ${this.renderSuppliersTable()}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <div class="pagination-container">
                    ${this.renderPagination()}
                </div>

                <!-- الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulk-actions" style="display: none;">
                    <div class="bulk-actions-content">
                        <span id="selected-count">0 مورد محدد</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-sm btn-warning" onclick="suppliersManager.bulkToggleStatus()">
                                <i class="fas fa-toggle-on"></i>
                                تبديل الحالة
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="suppliersManager.bulkDelete()">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل مورد -->
            <div id="supplier-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3 id="supplier-modal-title">إضافة مورد جديد</h3>
                        <button class="close-btn" onclick="closeModal('supplier-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="supplier-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم المورد *</label>
                                    <input type="text" id="supplier-name" required>
                                </div>
                                <div class="form-group">
                                    <label>اسم الشركة</label>
                                    <input type="text" id="supplier-company">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" id="supplier-phone" required>
                                </div>
                                <div class="form-group">
                                    <label>البريد الإلكتروني</label>
                                    <input type="email" id="supplier-email">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الهاتف الثاني</label>
                                    <input type="tel" id="supplier-phone2">
                                </div>
                                <div class="form-group">
                                    <label>الفاكس</label>
                                    <input type="tel" id="supplier-fax">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>العنوان</label>
                                <textarea id="supplier-address" rows="3"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المدينة</label>
                                    <input type="text" id="supplier-city">
                                </div>
                                <div class="form-group">
                                    <label>الرمز البريدي</label>
                                    <input type="text" id="supplier-postal">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الرقم الضريبي</label>
                                    <input type="text" id="supplier-tax-number">
                                </div>
                                <div class="form-group">
                                    <label>السجل التجاري</label>
                                    <input type="text" id="supplier-commercial-register">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>شروط الدفع (بالأيام)</label>
                                    <input type="number" id="supplier-payment-terms" min="0" value="30">
                                </div>
                                <div class="form-group">
                                    <label>حد الائتمان</label>
                                    <input type="number" id="supplier-credit-limit" min="0" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="supplier-notes" rows="2"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="supplier-active" checked>
                                    <span>مورد نشط</span>
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('supplier-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="suppliersManager.saveSupplier()">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- نافذة عرض تفاصيل المورد -->
            <div id="supplier-details-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل المورد</h3>
                        <button class="close-btn" onclick="closeModal('supplier-details-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body" id="supplier-details-content">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة المدفوعات -->
            <div id="supplier-payment-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="supplier-payment-modal-title">تسجيل دفعة للمورد</h3>
                        <button class="close-btn" onclick="closeModal('supplier-payment-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="supplier-payment-form">
                            <div class="form-group">
                                <label>المورد:</label>
                                <input type="text" id="supplier-payment-name" readonly>
                            </div>
                            <div class="form-group">
                                <label>الرصيد الحالي:</label>
                                <input type="text" id="supplier-payment-balance" readonly>
                            </div>
                            <div class="form-group">
                                <label>نوع العملية *</label>
                                <select id="supplier-payment-type" required>
                                    <option value="">اختر نوع العملية</option>
                                    <option value="payment">دفعة للمورد</option>
                                    <option value="charge">إضافة مستحقات</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>المبلغ *</label>
                                <input type="number" id="supplier-payment-amount" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label>طريقة الدفع</label>
                                <select id="supplier-payment-method">
                                    <option value="cash">نقداً</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>رقم المرجع</label>
                                <input type="text" id="supplier-payment-reference">
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="supplier-payment-notes" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('supplier-payment-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="suppliersManager.saveSupplierPayment()">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- نافذة تقرير المستحقات -->
            <div id="payables-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تقرير المستحقات للموردين</h3>
                        <button class="close-btn" onclick="closeModal('payables-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="payables-report-content">
                            <!-- سيتم تحميل التقرير هنا -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('payables-modal')">إغلاق</button>
                        <button class="btn btn-primary" onclick="suppliersManager.printPayablesReport()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // البحث في الموردين
        const searchInput = DOMUtils.$('#suppliers-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchSuppliers(e.target.value);
            });
        }

        // مستمعي أحداث الجدول
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('supplier-checkbox')) {
                this.updateBulkActions();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (window.app.currentPage === 'suppliers') {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'n':
                            e.preventDefault();
                            this.showAddSupplierModal();
                            break;
                        case 'f':
                            e.preventDefault();
                            searchInput?.focus();
                            break;
                    }
                }
            }
        });
    }

    // عرض جدول الموردين
    renderSuppliersTable() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageSuppliers = this.filteredSuppliers.slice(startIndex, endIndex);

        if (pageSuppliers.length === 0) {
            return `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="no-data">
                            <i class="fas fa-truck"></i>
                            <p>لا يوجد موردين</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return pageSuppliers.map(supplier => {
            const hasPayables = supplier.balance > 0;

            return `
                <tr class="supplier-row ${!supplier.active ? 'inactive' : ''}" data-supplier-id="${supplier.id}">
                    <td>
                        <input type="checkbox" class="supplier-checkbox" value="${supplier.id}">
                    </td>
                    <td>
                        <div class="supplier-name-cell">
                            <strong>${supplier.name}</strong>
                        </div>
                    </td>
                    <td>${supplier.company || '-'}</td>
                    <td>${supplier.phone || '-'}</td>
                    <td>${supplier.email || '-'}</td>
                    <td class="balance-cell">
                        <span class="balance ${hasPayables ? 'payable' : 'zero'}">
                            ${NumberUtils.formatCurrency(supplier.balance)}
                            ${hasPayables ? ' (مستحق)' : ''}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${supplier.active ? 'active' : 'inactive'}">
                            ${supplier.active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="suppliersManager.viewSupplier('${supplier.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="suppliersManager.editSupplier('${supplier.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="suppliersManager.showSupplierPaymentModal('${supplier.id}')" title="دفعة">
                                <i class="fas fa-money-bill"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="suppliersManager.toggleSupplierStatus('${supplier.id}')" title="تبديل الحالة">
                                <i class="fas fa-toggle-${supplier.active ? 'on' : 'off'}"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="suppliersManager.deleteSupplier('${supplier.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // عرض التنقل بين الصفحات
    renderPagination() {
        const totalPages = Math.ceil(this.filteredSuppliers.length / this.itemsPerPage);

        if (totalPages <= 1) return '';

        let pagination = '<div class="pagination">';

        if (this.currentPage > 1) {
            pagination += `<button class="page-btn" onclick="suppliersManager.goToPage(${this.currentPage - 1})">السابق</button>`;
        }

        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                pagination += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination += `<button class="page-btn" onclick="suppliersManager.goToPage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination += '<span class="page-dots">...</span>';
            }
        }

        if (this.currentPage < totalPages) {
            pagination += `<button class="page-btn" onclick="suppliersManager.goToPage(${this.currentPage + 1})">التالي</button>`;
        }

        pagination += '</div>';
        return pagination;
    }

    // البحث في الموردين
    searchSuppliers(query = '') {
        const searchInput = DOMUtils.$('#suppliers-search');
        const searchQuery = query || searchInput?.value || '';

        if (searchQuery.trim() === '') {
            this.filteredSuppliers = [...this.suppliers];
        } else {
            const searchTerm = searchQuery.toLowerCase();
            this.filteredSuppliers = this.suppliers.filter(supplier =>
                supplier.name.toLowerCase().includes(searchTerm) ||
                (supplier.company && supplier.company.toLowerCase().includes(searchTerm)) ||
                (supplier.phone && supplier.phone.includes(searchTerm)) ||
                (supplier.email && supplier.email.toLowerCase().includes(searchTerm)) ||
                (supplier.address && supplier.address.toLowerCase().includes(searchTerm))
            );
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // فلترة حسب الحالة
    filterByStatus() {
        const statusSelect = DOMUtils.$('#status-filter');
        const status = statusSelect?.value;

        switch (status) {
            case 'active':
                this.filteredSuppliers = this.suppliers.filter(s => s.active);
                break;
            case 'inactive':
                this.filteredSuppliers = this.suppliers.filter(s => !s.active);
                break;
            case 'payable':
                this.filteredSuppliers = this.suppliers.filter(s => s.balance > 0);
                break;
            default:
                this.filteredSuppliers = [...this.suppliers];
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // ترتيب الموردين
    sortSuppliers() {
        const sortSelect = DOMUtils.$('#sort-by');
        this.sortBy = sortSelect?.value || 'name';

        this.filteredSuppliers.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            if (this.sortBy === 'createdAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return this.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            }

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
            }
        });

        this.updateTable();
    }

    // تبديل ترتيب الفرز
    toggleSortOrder() {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        const sortBtn = DOMUtils.$('.sort-order-btn i');
        if (sortBtn) {
            sortBtn.className = `fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}`;
        }
        this.sortSuppliers();
    }

    // مسح الفلاتر
    clearFilters() {
        DOMUtils.$('#suppliers-search').value = '';
        DOMUtils.$('#status-filter').value = '';
        DOMUtils.$('#sort-by').value = 'name';

        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.filteredSuppliers = [...this.suppliers];
        this.currentPage = 1;

        this.updateTable();
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الجدول
    updateTable() {
        const tableBody = DOMUtils.$('#suppliers-table-body');
        const paginationContainer = DOMUtils.$('.pagination-container');
        const suppliersCount = DOMUtils.$('.suppliers-count');

        if (tableBody) {
            tableBody.innerHTML = this.renderSuppliersTable();
        }

        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }

        if (suppliersCount) {
            suppliersCount.textContent = `${this.filteredSuppliers.length} مورد`;
        }

        this.updateBulkActions();
    }

    // إظهار نافذة إضافة مورد
    showAddSupplierModal() {
        this.currentSupplier = null;
        this.resetSupplierForm();
        DOMUtils.$('#supplier-modal-title').textContent = 'إضافة مورد جديد';
        DOMUtils.addClass('#supplier-modal', 'show');

        setTimeout(() => {
            DOMUtils.$('#supplier-name')?.focus();
        }, 100);
    }

    // تعديل مورد
    editSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        this.currentSupplier = supplier;
        this.fillSupplierForm(supplier);
        DOMUtils.$('#supplier-modal-title').textContent = 'تعديل المورد';
        DOMUtils.addClass('#supplier-modal', 'show');
    }

    // عرض تفاصيل المورد
    viewSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const purchases = db.getPurchases().filter(purchase => purchase.supplierId === supplierId);
        const payments = db.get('cm_supplier_payments', []).filter(payment => payment.supplierId === supplierId);

        const detailsContent = DOMUtils.$('#supplier-details-content');

        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="supplier-details">
                    <div class="supplier-header">
                        <div class="supplier-basic-info">
                            <h3>${supplier.name}</h3>
                            ${supplier.company ? `<p class="supplier-company">${supplier.company}</p>` : ''}
                            <div class="supplier-balance ${supplier.balance > 0 ? 'payable' : 'zero'}">
                                <strong>الرصيد: ${NumberUtils.formatCurrency(supplier.balance)}</strong>
                                ${supplier.balance > 0 ? ' (مستحق الدفع)' : ''}
                            </div>
                        </div>
                        <div class="supplier-actions">
                            <button class="btn btn-primary" onclick="suppliersManager.editSupplier('${supplier.id}')">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="btn btn-success" onclick="suppliersManager.showSupplierPaymentModal('${supplier.id}')">
                                <i class="fas fa-money-bill"></i>
                                دفعة
                            </button>
                        </div>
                    </div>

                    <div class="supplier-info-grid">
                        <div class="info-section">
                            <h4>معلومات الاتصال</h4>
                            <div class="info-item">
                                <label>الهاتف الأساسي:</label>
                                <span>${supplier.phone || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>الهاتف الثاني:</label>
                                <span>${supplier.phone2 || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>البريد الإلكتروني:</label>
                                <span>${supplier.email || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>الفاكس:</label>
                                <span>${supplier.fax || 'غير محدد'}</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>العنوان</h4>
                            <div class="info-item">
                                <label>العنوان:</label>
                                <span>${supplier.address || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>المدينة:</label>
                                <span>${supplier.city || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>الرمز البريدي:</label>
                                <span>${supplier.postalCode || 'غير محدد'}</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>المعلومات التجارية</h4>
                            <div class="info-item">
                                <label>الرقم الضريبي:</label>
                                <span>${supplier.taxNumber || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>السجل التجاري:</label>
                                <span>${supplier.commercialRegister || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>شروط الدفع:</label>
                                <span>${supplier.paymentTerms || 30} يوم</span>
                            </div>
                            <div class="info-item">
                                <label>حد الائتمان:</label>
                                <span>${NumberUtils.formatCurrency(supplier.creditLimit || 0)}</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>معلومات الحساب</h4>
                            <div class="info-item">
                                <label>الحالة:</label>
                                <span class="status-badge ${supplier.active ? 'active' : 'inactive'}">
                                    ${supplier.active ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                            <div class="info-item">
                                <label>تاريخ الإضافة:</label>
                                <span>${DateUtils.formatDate(supplier.createdAt, 'dd/mm/yyyy hh:mm')}</span>
                            </div>
                        </div>
                    </div>

                    ${supplier.notes ? `
                        <div class="supplier-notes">
                            <h4>ملاحظات</h4>
                            <p>${supplier.notes}</p>
                        </div>
                    ` : ''}

                    <div class="supplier-transactions">
                        <h4>آخر المعاملات</h4>
                        <div class="transactions-list">
                            ${this.renderSupplierTransactions(purchases, payments)}
                        </div>
                    </div>
                </div>
            `;
        }

        DOMUtils.addClass('#supplier-details-modal', 'show');
    }

    // عرض معاملات المورد
    renderSupplierTransactions(purchases, payments) {
        const allTransactions = [];

        // إضافة المشتريات
        purchases.forEach(purchase => {
            allTransactions.push({
                type: 'purchase',
                date: purchase.createdAt,
                amount: purchase.total,
                description: `فاتورة شراء #${purchase.purchaseNumber}`,
                paymentMethod: purchase.paymentMethod
            });
        });

        // إضافة الدفعات
        payments.forEach(payment => {
            allTransactions.push({
                type: payment.type,
                date: payment.createdAt,
                amount: payment.amount,
                description: payment.notes || (payment.type === 'payment' ? 'دفعة للمورد' : 'إضافة مستحقات'),
                paymentMethod: payment.method
            });
        });

        // ترتيب حسب التاريخ
        allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        if (allTransactions.length === 0) {
            return '<p class="text-center text-secondary">لا توجد معاملات</p>';
        }

        return allTransactions.slice(0, 10).map(transaction => `
            <div class="transaction-item ${transaction.type}">
                <div class="transaction-icon">
                    <i class="fas fa-${transaction.type === 'purchase' ? 'shopping-cart' : transaction.type === 'payment' ? 'arrow-up' : 'arrow-down'}"></i>
                </div>
                <div class="transaction-content">
                    <h5>${transaction.description}</h5>
                    <p>
                        ${DateUtils.formatDate(transaction.date, 'dd/mm/yyyy hh:mm')} |
                        ${transaction.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}
                    </p>
                </div>
                <div class="transaction-amount ${transaction.type}">
                    ${transaction.type === 'payment' ? '-' : '+'}${NumberUtils.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    // حذف مورد
    deleteSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        // التحقق من وجود معاملات
        const purchases = db.getPurchases().filter(purchase => purchase.supplierId === supplierId);
        if (purchases.length > 0) {
            app.showNotification('لا يمكن حذف المورد لوجود معاملات مرتبطة به', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف المورد "${supplier.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                if (db.deleteSupplier(supplierId)) {
                    app.showNotification('تم حذف المورد بنجاح', 'success');
                    this.loadData();
                    this.updateTable();
                } else {
                    app.showNotification('فشل في حذف المورد', 'error');
                }
            }
        );
    }

    // تبديل حالة المورد
    toggleSupplierStatus(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const updatedSupplier = { active: !supplier.active };

        if (db.updateSupplier(supplierId, updatedSupplier)) {
            app.showNotification(
                `تم ${updatedSupplier.active ? 'تفعيل' : 'إلغاء تفعيل'} المورد`,
                'success'
            );
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في تحديث حالة المورد', 'error');
        }
    }

    // إعادة تعيين نموذج المورد
    resetSupplierForm() {
        const form = DOMUtils.$('#supplier-form');
        if (form) {
            form.reset();
        }

        DOMUtils.$('#supplier-active').checked = true;
        DOMUtils.$('#supplier-payment-terms').value = '30';
        DOMUtils.$('#supplier-credit-limit').value = '0';
    }

    // ملء نموذج المورد
    fillSupplierForm(supplier) {
        DOMUtils.$('#supplier-name').value = supplier.name || '';
        DOMUtils.$('#supplier-company').value = supplier.company || '';
        DOMUtils.$('#supplier-phone').value = supplier.phone || '';
        DOMUtils.$('#supplier-phone2').value = supplier.phone2 || '';
        DOMUtils.$('#supplier-email').value = supplier.email || '';
        DOMUtils.$('#supplier-fax').value = supplier.fax || '';
        DOMUtils.$('#supplier-address').value = supplier.address || '';
        DOMUtils.$('#supplier-city').value = supplier.city || '';
        DOMUtils.$('#supplier-postal').value = supplier.postalCode || '';
        DOMUtils.$('#supplier-tax-number').value = supplier.taxNumber || '';
        DOMUtils.$('#supplier-commercial-register').value = supplier.commercialRegister || '';
        DOMUtils.$('#supplier-payment-terms').value = supplier.paymentTerms || 30;
        DOMUtils.$('#supplier-credit-limit').value = supplier.creditLimit || 0;
        DOMUtils.$('#supplier-notes').value = supplier.notes || '';
        DOMUtils.$('#supplier-active').checked = supplier.active !== false;
    }

    // حفظ المورد
    saveSupplier() {
        const name = DOMUtils.$('#supplier-name')?.value?.trim();
        const company = DOMUtils.$('#supplier-company')?.value?.trim();
        const phone = DOMUtils.$('#supplier-phone')?.value?.trim();
        const phone2 = DOMUtils.$('#supplier-phone2')?.value?.trim();
        const email = DOMUtils.$('#supplier-email')?.value?.trim();
        const fax = DOMUtils.$('#supplier-fax')?.value?.trim();
        const address = DOMUtils.$('#supplier-address')?.value?.trim();
        const city = DOMUtils.$('#supplier-city')?.value?.trim();
        const postalCode = DOMUtils.$('#supplier-postal')?.value?.trim();
        const taxNumber = DOMUtils.$('#supplier-tax-number')?.value?.trim();
        const commercialRegister = DOMUtils.$('#supplier-commercial-register')?.value?.trim();
        const paymentTerms = parseInt(DOMUtils.$('#supplier-payment-terms')?.value || 30);
        const creditLimit = parseFloat(DOMUtils.$('#supplier-credit-limit')?.value || 0);
        const notes = DOMUtils.$('#supplier-notes')?.value?.trim();
        const active = DOMUtils.$('#supplier-active')?.checked;

        // التحقق من صحة البيانات
        const validation = this.validateSupplierData({
            name, phone, email, paymentTerms, creditLimit
        });

        if (!validation.valid) {
            app.showNotification(validation.message, 'error');
            return;
        }

        const supplierData = {
            name,
            company,
            phone,
            phone2,
            email,
            fax,
            address,
            city,
            postalCode,
            taxNumber,
            commercialRegister,
            paymentTerms,
            creditLimit,
            notes,
            active
        };

        let success = false;

        if (this.currentSupplier) {
            // تحديث مورد موجود
            success = db.updateSupplier(this.currentSupplier.id, supplierData);
        } else {
            // إضافة مورد جديد
            success = db.addSupplier(supplierData);
        }

        if (success) {
            app.showNotification(
                this.currentSupplier ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح',
                'success'
            );

            app.closeModal('supplier-modal');
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في حفظ المورد', 'error');
        }
    }

    // التحقق من صحة بيانات المورد
    validateSupplierData(data) {
        if (!data.name) {
            return { valid: false, message: 'اسم المورد مطلوب' };
        }

        if (!data.phone) {
            return { valid: false, message: 'رقم الهاتف مطلوب' };
        }

        if (data.email && !StringUtils.isValidEmail(data.email)) {
            return { valid: false, message: 'البريد الإلكتروني غير صحيح' };
        }

        if (data.phone && !StringUtils.isValidPhone(data.phone)) {
            return { valid: false, message: 'رقم الهاتف غير صحيح' };
        }

        if (data.paymentTerms < 0) {
            return { valid: false, message: 'شروط الدفع لا يمكن أن تكون سالبة' };
        }

        if (data.creditLimit < 0) {
            return { valid: false, message: 'حد الائتمان لا يمكن أن يكون سالباً' };
        }

        return { valid: true };
    }

    // إظهار نافذة دفعات المورد
    showSupplierPaymentModal(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        this.currentSupplier = supplier;

        // ملء البيانات الأساسية
        DOMUtils.$('#supplier-payment-name').value = supplier.name;
        DOMUtils.$('#supplier-payment-balance').value = NumberUtils.formatCurrency(supplier.balance);

        // إعادة تعيين النموذج
        DOMUtils.$('#supplier-payment-type').value = '';
        DOMUtils.$('#supplier-payment-amount').value = '';
        DOMUtils.$('#supplier-payment-method').value = 'cash';
        DOMUtils.$('#supplier-payment-reference').value = '';
        DOMUtils.$('#supplier-payment-notes').value = '';

        DOMUtils.addClass('#supplier-payment-modal', 'show');
    }

    // حفظ دفعة المورد
    saveSupplierPayment() {
        const paymentType = DOMUtils.$('#supplier-payment-type')?.value;
        const amount = parseFloat(DOMUtils.$('#supplier-payment-amount')?.value || 0);
        const method = DOMUtils.$('#supplier-payment-method')?.value;
        const reference = DOMUtils.$('#supplier-payment-reference')?.value?.trim();
        const notes = DOMUtils.$('#supplier-payment-notes')?.value?.trim();

        if (!paymentType) {
            app.showNotification('يرجى اختيار نوع العملية', 'error');
            return;
        }

        if (amount <= 0) {
            app.showNotification('المبلغ يجب أن يكون أكبر من صفر', 'error');
            return;
        }

        // حساب الرصيد الجديد
        let newBalance = this.currentSupplier.balance;
        if (paymentType === 'payment') {
            newBalance -= amount; // تقليل المستحقات
        } else {
            newBalance += amount; // زيادة المستحقات
        }

        // تحديث رصيد المورد
        if (db.updateSupplier(this.currentSupplier.id, { balance: newBalance })) {
            // حفظ سجل الدفعة
            const payment = {
                supplierId: this.currentSupplier.id,
                type: paymentType,
                amount: amount,
                method: method,
                reference: reference,
                notes: notes,
                createdAt: new Date().toISOString()
            };

            const payments = db.get('cm_supplier_payments', []);
            payments.push(payment);
            db.save('cm_supplier_payments', payments);

            app.showNotification('تم تسجيل العملية بنجاح', 'success');
            app.closeModal('supplier-payment-modal');

            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في تسجيل العملية', 'error');
        }
    }

    // تحديد/إلغاء تحديد الكل
    toggleSelectAll() {
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const supplierCheckboxes = DOMUtils.$$('.supplier-checkbox');

        supplierCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    }

    // تحديث الإجراءات المجمعة
    updateBulkActions() {
        const selectedCheckboxes = DOMUtils.$$('.supplier-checkbox:checked');
        const bulkActions = DOMUtils.$('#bulk-actions');
        const selectedCount = DOMUtils.$('#selected-count');

        if (selectedCheckboxes.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${selectedCheckboxes.length} مورد محدد`;
        } else {
            bulkActions.style.display = 'none';
        }

        // تحديث حالة "تحديد الكل"
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const allCheckboxes = DOMUtils.$$('.supplier-checkbox');

        if (selectedCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // تبديل حالة الموردين المحددين
    bulkToggleStatus() {
        const selectedIds = Array.from(DOMUtils.$$('.supplier-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي مورد', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد العملية',
            `هل تريد تبديل حالة ${selectedIds.length} مورد؟`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    const supplier = this.suppliers.find(s => s.id === id);
                    if (supplier) {
                        if (db.updateSupplier(id, { active: !supplier.active })) {
                            successCount++;
                        }
                    }
                });

                app.showNotification(`تم تحديث ${successCount} مورد`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // حذف الموردين المحددين
    bulkDelete() {
        const selectedIds = Array.from(DOMUtils.$$('.supplier-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي مورد', 'warning');
            return;
        }

        // التحقق من وجود معاملات
        const purchases = db.getPurchases();
        const hasTransactions = selectedIds.some(id =>
            purchases.some(purchase => purchase.supplierId === id)
        );

        if (hasTransactions) {
            app.showNotification('لا يمكن حذف موردين لديهم معاملات', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف ${selectedIds.length} مورد؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    if (db.deleteSupplier(id)) {
                        successCount++;
                    }
                });

                app.showNotification(`تم حذف ${successCount} مورد`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // إظهار تقرير المستحقات
    showPayablesReport() {
        const payableSuppliers = this.suppliers.filter(supplier =>
            supplier.balance > 0
        );

        const reportContent = DOMUtils.$('#payables-report-content');

        if (reportContent) {
            const totalPayables = payableSuppliers.reduce((sum, supplier) => sum + supplier.balance, 0);

            reportContent.innerHTML = `
                <div class="payables-report">
                    <div class="report-summary">
                        <h4>ملخص المستحقات</h4>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <label>عدد الموردين:</label>
                                <span>${payableSuppliers.length}</span>
                            </div>
                            <div class="stat-item">
                                <label>إجمالي المستحقات:</label>
                                <span class="payable-amount">${NumberUtils.formatCurrency(totalPayables)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="payables-table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم المورد</th>
                                    <th>الشركة</th>
                                    <th>الهاتف</th>
                                    <th>المبلغ المستحق</th>
                                    <th>شروط الدفع</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${payableSuppliers.map(supplier => `
                                    <tr>
                                        <td>${supplier.name}</td>
                                        <td>${supplier.company || '-'}</td>
                                        <td>${supplier.phone || '-'}</td>
                                        <td class="payable-amount">${NumberUtils.formatCurrency(supplier.balance)}</td>
                                        <td>${supplier.paymentTerms || 30} يوم</td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="suppliersManager.showSupplierPaymentModal('${supplier.id}')">
                                                <i class="fas fa-money-bill"></i>
                                                دفعة
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        DOMUtils.addClass('#payables-modal', 'show');
    }

    // طباعة تقرير المستحقات
    printPayablesReport() {
        window.print();
    }

    // تصدير الموردين
    exportSuppliers() {
        const data = this.filteredSuppliers.map(supplier => ({
            'الاسم': supplier.name,
            'الشركة': supplier.company || '',
            'الهاتف': supplier.phone || '',
            'البريد الإلكتروني': supplier.email || '',
            'العنوان': supplier.address || '',
            'المدينة': supplier.city || '',
            'الرصيد': supplier.balance,
            'شروط الدفع': supplier.paymentTerms || 30,
            'حد الائتمان': supplier.creditLimit || 0,
            'الحالة': supplier.active ? 'نشط' : 'غير نشط',
            'تاريخ الإضافة': DateUtils.formatDate(supplier.createdAt),
            'ملاحظات': supplier.notes || ''
        }));

        const csvContent = this.convertToCSV(data);
        const filename = `suppliers-${DateUtils.getCurrentDate()}.csv`;

        Utils.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
        app.showNotification('تم تصدير الموردين بنجاح', 'success');
    }

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [];

        // إضافة العناوين
        csvRows.push(headers.join(','));

        // إضافة البيانات
        data.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }
}

// إنشاء مثيل من مدير الموردين
const suppliersManager = new SuppliersManager();

// تصدير مدير الموردين للاستخدام العام
window.suppliersManager = suppliersManager;
