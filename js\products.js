/**
 * Cybernet Magasin - إدارة المنتجات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class ProductsManager {
    constructor() {
        this.products = [];
        this.categories = [];
        this.filteredProducts = [];
        this.currentProduct = null;
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.init();
    }

    // تهيئة مدير المنتجات
    init() {
        this.loadData();
    }

    // تحميل البيانات
    loadData() {
        this.products = db.getProducts();
        this.categories = db.getCategories();
        this.filteredProducts = [...this.products];
    }

    // عرض واجهة إدارة المنتجات
    render() {
        const container = DOMUtils.$('#products-page');
        if (!container) return;

        this.loadData();

        container.innerHTML = `
            <div class="products-container">
                <!-- شريط الأدوات -->
                <div class="products-toolbar">
                    <div class="toolbar-left">
                        <h2>إدارة المنتجات</h2>
                        <span class="products-count">${this.products.length} منتج</span>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="productsManager.showAddProductModal()">
                            <i class="fas fa-plus"></i>
                            إضافة منتج جديد
                        </button>
                        <button class="btn btn-secondary" onclick="productsManager.showImportModal()">
                            <i class="fas fa-upload"></i>
                            استيراد
                        </button>
                        <button class="btn btn-info" onclick="productsManager.exportProducts()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="products-filters">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <div class="search-box">
                            <input type="text" id="products-search" placeholder="ابحث بالاسم، الوصف، أو الباركود...">
                            <button onclick="productsManager.searchProducts()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>الفئة:</label>
                        <select id="category-filter" onchange="productsManager.filterByCategory()">
                            <option value="">جميع الفئات</option>
                            ${this.renderCategoryOptions()}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" onchange="productsManager.filterByStatus()">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="low-stock">مخزون منخفض</option>
                            <option value="out-of-stock">نفد المخزون</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>ترتيب حسب:</label>
                        <select id="sort-by" onchange="productsManager.sortProducts()">
                            <option value="name">الاسم</option>
                            <option value="price">السعر</option>
                            <option value="quantity">الكمية</option>
                            <option value="createdAt">تاريخ الإضافة</option>
                        </select>
                        <button class="sort-order-btn" onclick="productsManager.toggleSortOrder()">
                            <i class="fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}"></i>
                        </button>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-sm btn-secondary" onclick="productsManager.clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="products-table-container">
                    <table class="table products-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" onchange="productsManager.toggleSelectAll()">
                                </th>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            ${this.renderProductsTable()}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <div class="pagination-container">
                    ${this.renderPagination()}
                </div>

                <!-- الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulk-actions" style="display: none;">
                    <div class="bulk-actions-content">
                        <span id="selected-count">0 منتج محدد</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-sm btn-warning" onclick="productsManager.bulkToggleStatus()">
                                <i class="fas fa-toggle-on"></i>
                                تبديل الحالة
                            </button>
                            <button class="btn btn-sm btn-info" onclick="productsManager.bulkUpdateCategory()">
                                <i class="fas fa-tags"></i>
                                تغيير الفئة
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="productsManager.bulkDelete()">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل منتج -->
            <div id="product-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3 id="product-modal-title">إضافة منتج جديد</h3>
                        <button class="close-btn" onclick="closeModal('product-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="product-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم المنتج *</label>
                                    <input type="text" id="product-name" required>
                                </div>
                                <div class="form-group">
                                    <label>الفئة *</label>
                                    <select id="product-category" required>
                                        <option value="">اختر الفئة</option>
                                        ${this.renderCategoryOptions()}
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>السعر *</label>
                                    <input type="number" id="product-price" step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label>الكمية *</label>
                                    <input type="number" id="product-quantity" min="0" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الحد الأدنى للمخزون</label>
                                    <input type="number" id="product-min-stock" min="0" value="10">
                                </div>
                                <div class="form-group">
                                    <label>الباركود</label>
                                    <input type="text" id="product-barcode">
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="productsManager.generateBarcode()">
                                        توليد
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>الوصف</label>
                                <textarea id="product-description" rows="3"></textarea>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>تاريخ انتهاء الصلاحية</label>
                                    <input type="date" id="product-expiry">
                                </div>
                                <div class="form-group">
                                    <label>الوحدة</label>
                                    <select id="product-unit">
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلو">كيلو</option>
                                        <option value="لتر">لتر</option>
                                        <option value="علبة">علبة</option>
                                        <option value="كيس">كيس</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>صورة المنتج</label>
                                <div class="image-upload">
                                    <input type="file" id="product-image" accept="image/*" onchange="productsManager.previewImage()">
                                    <div class="image-preview" id="image-preview">
                                        <i class="fas fa-image"></i>
                                        <span>اختر صورة</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="product-active" checked>
                                    <span>منتج نشط</span>
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('product-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="productsManager.saveProduct()">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- نافذة عرض تفاصيل المنتج -->
            <div id="product-details-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تفاصيل المنتج</h3>
                        <button class="close-btn" onclick="closeModal('product-details-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body" id="product-details-content">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>

            <!-- نافذة الاستيراد -->
            <div id="import-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>استيراد المنتجات</h3>
                        <button class="close-btn" onclick="closeModal('import-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="import-section">
                            <p>يمكنك استيراد المنتجات من ملف CSV أو Excel</p>
                            <div class="file-upload">
                                <input type="file" id="import-file" accept=".csv,.xlsx,.xls">
                                <label for="import-file" class="file-upload-label">
                                    <i class="fas fa-upload"></i>
                                    اختر ملف للاستيراد
                                </label>
                            </div>
                            <div class="import-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="update-existing" checked>
                                    <span>تحديث المنتجات الموجودة</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('import-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="productsManager.importProducts()">استيراد</button>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // البحث في المنتجات
        const searchInput = DOMUtils.$('#products-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchProducts(e.target.value);
            });
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (window.app.currentPage === 'products') {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'n':
                            e.preventDefault();
                            this.showAddProductModal();
                            break;
                        case 'f':
                            e.preventDefault();
                            searchInput?.focus();
                            break;
                    }
                }
            }
        });
    }

    // عرض خيارات الفئات
    renderCategoryOptions() {
        return this.categories.map(category =>
            `<option value="${category.id}">${category.name}</option>`
        ).join('');
    }

    // عرض جدول المنتجات
    renderProductsTable() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

        if (pageProducts.length === 0) {
            return `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="no-data">
                            <i class="fas fa-box-open"></i>
                            <p>لا توجد منتجات</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return pageProducts.map(product => {
            const category = this.categories.find(c => c.id === product.categoryId);
            const isLowStock = product.quantity <= (product.minStock || 10);
            const isOutOfStock = product.quantity === 0;

            return `
                <tr class="product-row ${!product.active ? 'inactive' : ''}" data-product-id="${product.id}">
                    <td>
                        <input type="checkbox" class="product-checkbox" value="${product.id}">
                    </td>
                    <td>
                        <div class="product-image-cell">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.name}">` :
                                '<i class="fas fa-box"></i>'
                            }
                        </div>
                    </td>
                    <td>
                        <div class="product-name-cell">
                            <strong>${product.name}</strong>
                            ${product.barcode ? `<small>الباركود: ${product.barcode}</small>` : ''}
                        </div>
                    </td>
                    <td>${category ? category.name : 'غير محدد'}</td>
                    <td class="price-cell">${NumberUtils.formatCurrency(product.price)}</td>
                    <td class="quantity-cell">
                        <span class="quantity ${isOutOfStock ? 'out-of-stock' : isLowStock ? 'low-stock' : ''}">
                            ${product.quantity}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${product.active ? 'active' : 'inactive'}">
                            ${product.active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="productsManager.viewProduct('${product.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="productsManager.editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="productsManager.toggleProductStatus('${product.id}')" title="تبديل الحالة">
                                <i class="fas fa-toggle-${product.active ? 'on' : 'off'}"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="productsManager.deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // عرض التنقل بين الصفحات
    renderPagination() {
        const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);

        if (totalPages <= 1) return '';

        let pagination = '<div class="pagination">';

        // الصفحة السابقة
        if (this.currentPage > 1) {
            pagination += `<button class="page-btn" onclick="productsManager.goToPage(${this.currentPage - 1})">السابق</button>`;
        }

        // أرقام الصفحات
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                pagination += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination += `<button class="page-btn" onclick="productsManager.goToPage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination += '<span class="page-dots">...</span>';
            }
        }

        // الصفحة التالية
        if (this.currentPage < totalPages) {
            pagination += `<button class="page-btn" onclick="productsManager.goToPage(${this.currentPage + 1})">التالي</button>`;
        }

        pagination += '</div>';

        return pagination;
    }

    // البحث في المنتجات
    searchProducts(query = '') {
        const searchInput = DOMUtils.$('#products-search');
        const searchQuery = query || searchInput?.value || '';

        if (searchQuery.trim() === '') {
            this.filteredProducts = [...this.products];
        } else {
            this.filteredProducts = db.searchProducts(searchQuery);
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // فلترة حسب الفئة
    filterByCategory() {
        const categorySelect = DOMUtils.$('#category-filter');
        const categoryId = categorySelect?.value;

        if (!categoryId) {
            this.filteredProducts = [...this.products];
        } else {
            this.filteredProducts = this.products.filter(product =>
                product.categoryId == categoryId
            );
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // فلترة حسب الحالة
    filterByStatus() {
        const statusSelect = DOMUtils.$('#status-filter');
        const status = statusSelect?.value;

        switch (status) {
            case 'active':
                this.filteredProducts = this.products.filter(p => p.active);
                break;
            case 'inactive':
                this.filteredProducts = this.products.filter(p => !p.active);
                break;
            case 'low-stock':
                this.filteredProducts = this.products.filter(p =>
                    p.quantity <= (p.minStock || 10) && p.quantity > 0
                );
                break;
            case 'out-of-stock':
                this.filteredProducts = this.products.filter(p => p.quantity === 0);
                break;
            default:
                this.filteredProducts = [...this.products];
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // ترتيب المنتجات
    sortProducts() {
        const sortSelect = DOMUtils.$('#sort-by');
        this.sortBy = sortSelect?.value || 'name';

        this.filteredProducts.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            // معالجة خاصة للتواريخ
            if (this.sortBy === 'createdAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            // معالجة خاصة للأرقام
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return this.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // معالجة النصوص
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
            }
        });

        this.updateTable();
    }

    // تبديل ترتيب الفرز
    toggleSortOrder() {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        const sortBtn = DOMUtils.$('.sort-order-btn i');
        if (sortBtn) {
            sortBtn.className = `fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}`;
        }
        this.sortProducts();
    }

    // مسح الفلاتر
    clearFilters() {
        DOMUtils.$('#products-search').value = '';
        DOMUtils.$('#category-filter').value = '';
        DOMUtils.$('#status-filter').value = '';
        DOMUtils.$('#sort-by').value = 'name';

        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.filteredProducts = [...this.products];
        this.currentPage = 1;

        this.updateTable();
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الجدول
    updateTable() {
        const tableBody = DOMUtils.$('#products-table-body');
        const paginationContainer = DOMUtils.$('.pagination-container');
        const productsCount = DOMUtils.$('.products-count');

        if (tableBody) {
            tableBody.innerHTML = this.renderProductsTable();
        }

        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }

        if (productsCount) {
            productsCount.textContent = `${this.filteredProducts.length} منتج`;
        }

        this.updateBulkActions();
    }

    // إظهار نافذة إضافة منتج
    showAddProductModal() {
        this.currentProduct = null;
        this.resetProductForm();
        DOMUtils.$('#product-modal-title').textContent = 'إضافة منتج جديد';
        DOMUtils.addClass('#product-modal', 'show');

        setTimeout(() => {
            DOMUtils.$('#product-name')?.focus();
        }, 100);
    }

    // تعديل منتج
    editProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        this.currentProduct = product;
        this.fillProductForm(product);
        DOMUtils.$('#product-modal-title').textContent = 'تعديل المنتج';
        DOMUtils.addClass('#product-modal', 'show');
    }

    // عرض تفاصيل المنتج
    viewProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        const category = this.categories.find(c => c.id === product.categoryId);
        const detailsContent = DOMUtils.$('#product-details-content');

        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="product-details">
                    <div class="product-header">
                        ${product.image ?
                            `<img src="${product.image}" alt="${product.name}" class="product-detail-image">` :
                            '<div class="product-detail-placeholder"><i class="fas fa-box"></i></div>'
                        }
                        <div class="product-basic-info">
                            <h3>${product.name}</h3>
                            <p class="product-category">${category ? category.name : 'غير محدد'}</p>
                            <p class="product-price">${NumberUtils.formatCurrency(product.price)}</p>
                        </div>
                    </div>
                    <div class="product-info-grid">
                        <div class="info-item">
                            <label>الكمية المتاحة:</label>
                            <span class="quantity ${product.quantity === 0 ? 'out-of-stock' : product.quantity <= (product.minStock || 10) ? 'low-stock' : ''}">${product.quantity}</span>
                        </div>
                        <div class="info-item">
                            <label>الحد الأدنى للمخزون:</label>
                            <span>${product.minStock || 10}</span>
                        </div>
                        <div class="info-item">
                            <label>الوحدة:</label>
                            <span>${product.unit || 'قطعة'}</span>
                        </div>
                        <div class="info-item">
                            <label>الباركود:</label>
                            <span>${product.barcode || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <label>تاريخ انتهاء الصلاحية:</label>
                            <span>${product.expiryDate ? DateUtils.formatDate(product.expiryDate) : 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <label>الحالة:</label>
                            <span class="status-badge ${product.active ? 'active' : 'inactive'}">
                                ${product.active ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                        <div class="info-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${DateUtils.formatDate(product.createdAt, 'dd/mm/yyyy hh:mm')}</span>
                        </div>
                        <div class="info-item">
                            <label>آخر تحديث:</label>
                            <span>${product.updatedAt ? DateUtils.formatDate(product.updatedAt, 'dd/mm/yyyy hh:mm') : 'لم يتم التحديث'}</span>
                        </div>
                    </div>
                    ${product.description ? `
                        <div class="product-description">
                            <label>الوصف:</label>
                            <p>${product.description}</p>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        DOMUtils.addClass('#product-details-modal', 'show');
    }

    // حذف منتج
    deleteProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف المنتج "${product.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                if (db.deleteProduct(productId)) {
                    app.showNotification('تم حذف المنتج بنجاح', 'success');
                    this.loadData();
                    this.updateTable();
                } else {
                    app.showNotification('فشل في حذف المنتج', 'error');
                }
            }
        );
    }

    // تبديل حالة المنتج
    toggleProductStatus(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        const updatedProduct = { active: !product.active };

        if (db.updateProduct(productId, updatedProduct)) {
            app.showNotification(
                `تم ${updatedProduct.active ? 'تفعيل' : 'إلغاء تفعيل'} المنتج`,
                'success'
            );
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في تحديث حالة المنتج', 'error');
        }
    }

    // إعادة تعيين نموذج المنتج
    resetProductForm() {
        const form = DOMUtils.$('#product-form');
        if (form) {
            form.reset();
        }

        DOMUtils.$('#product-active').checked = true;
        DOMUtils.$('#product-min-stock').value = '10';
        DOMUtils.$('#product-unit').value = 'قطعة';

        const imagePreview = DOMUtils.$('#image-preview');
        if (imagePreview) {
            imagePreview.innerHTML = '<i class="fas fa-image"></i><span>اختر صورة</span>';
        }
    }

    // ملء نموذج المنتج
    fillProductForm(product) {
        DOMUtils.$('#product-name').value = product.name || '';
        DOMUtils.$('#product-category').value = product.categoryId || '';
        DOMUtils.$('#product-price').value = product.price || '';
        DOMUtils.$('#product-quantity').value = product.quantity || '';
        DOMUtils.$('#product-min-stock').value = product.minStock || 10;
        DOMUtils.$('#product-barcode').value = product.barcode || '';
        DOMUtils.$('#product-description').value = product.description || '';
        DOMUtils.$('#product-expiry').value = product.expiryDate || '';
        DOMUtils.$('#product-unit').value = product.unit || 'قطعة';
        DOMUtils.$('#product-active').checked = product.active !== false;

        // عرض الصورة إذا كانت موجودة
        if (product.image) {
            const imagePreview = DOMUtils.$('#image-preview');
            if (imagePreview) {
                imagePreview.innerHTML = `<img src="${product.image}" alt="معاينة">`;
            }
        }
    }

    // حفظ المنتج
    saveProduct() {
        const name = DOMUtils.$('#product-name')?.value?.trim();
        const categoryId = DOMUtils.$('#product-category')?.value;
        const price = parseFloat(DOMUtils.$('#product-price')?.value || 0);
        const quantity = parseInt(DOMUtils.$('#product-quantity')?.value || 0);
        const minStock = parseInt(DOMUtils.$('#product-min-stock')?.value || 10);
        const barcode = DOMUtils.$('#product-barcode')?.value?.trim();
        const description = DOMUtils.$('#product-description')?.value?.trim();
        const expiryDate = DOMUtils.$('#product-expiry')?.value;
        const unit = DOMUtils.$('#product-unit')?.value;
        const active = DOMUtils.$('#product-active')?.checked;

        // التحقق من صحة البيانات
        const validation = this.validateProductData({
            name, categoryId, price, quantity, minStock, barcode
        });

        if (!validation.valid) {
            app.showNotification(validation.message, 'error');
            return;
        }

        const productData = {
            name,
            categoryId,
            price,
            quantity,
            minStock,
            barcode,
            description,
            expiryDate,
            unit,
            active
        };

        // معالجة الصورة
        const imageFile = DOMUtils.$('#product-image')?.files[0];
        if (imageFile) {
            this.processProductImage(imageFile, productData);
        } else {
            this.saveProductData(productData);
        }
    }

    // التحقق من صحة بيانات المنتج
    validateProductData(data) {
        if (!data.name) {
            return { valid: false, message: 'اسم المنتج مطلوب' };
        }

        if (!data.categoryId) {
            return { valid: false, message: 'فئة المنتج مطلوبة' };
        }

        if (data.price <= 0) {
            return { valid: false, message: 'سعر المنتج يجب أن يكون أكبر من صفر' };
        }

        if (data.quantity < 0) {
            return { valid: false, message: 'كمية المنتج لا يمكن أن تكون سالبة' };
        }

        // التحقق من تفرد الباركود
        if (data.barcode) {
            const existingProduct = this.products.find(p =>
                p.barcode === data.barcode &&
                (!this.currentProduct || p.id !== this.currentProduct.id)
            );

            if (existingProduct) {
                return { valid: false, message: 'الباركود موجود مسبقاً' };
            }
        }

        return { valid: true };
    }

    // معالجة صورة المنتج
    processProductImage(imageFile, productData) {
        const reader = new FileReader();
        reader.onload = (e) => {
            productData.image = e.target.result;
            this.saveProductData(productData);
        };
        reader.onerror = () => {
            app.showNotification('فشل في قراءة الصورة', 'error');
        };
        reader.readAsDataURL(imageFile);
    }

    // حفظ بيانات المنتج
    saveProductData(productData) {
        let success = false;

        if (this.currentProduct) {
            // تحديث منتج موجود
            success = db.updateProduct(this.currentProduct.id, productData);
        } else {
            // إضافة منتج جديد
            success = db.addProduct(productData);
        }

        if (success) {
            app.showNotification(
                this.currentProduct ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح',
                'success'
            );

            app.closeModal('product-modal');
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في حفظ المنتج', 'error');
        }
    }

    // معاينة الصورة
    previewImage() {
        const fileInput = DOMUtils.$('#product-image');
        const imagePreview = DOMUtils.$('#image-preview');

        if (fileInput?.files && fileInput.files[0]) {
            const file = fileInput.files[0];

            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                app.showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                fileInput.value = '';
                return;
            }

            // التحقق من حجم الملف (2MB)
            if (file.size > 2 * 1024 * 1024) {
                app.showNotification('حجم الصورة يجب أن يكون أقل من 2 ميجابايت', 'error');
                fileInput.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                if (imagePreview) {
                    imagePreview.innerHTML = `<img src="${e.target.result}" alt="معاينة">`;
                }
            };
            reader.readAsDataURL(file);
        }
    }

    // توليد باركود
    generateBarcode() {
        const barcode = Date.now().toString();
        DOMUtils.$('#product-barcode').value = barcode;
    }

    // تحديد/إلغاء تحديد الكل
    toggleSelectAll() {
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const productCheckboxes = DOMUtils.$$('.product-checkbox');

        productCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    }

    // تحديث الإجراءات المجمعة
    updateBulkActions() {
        const selectedCheckboxes = DOMUtils.$$('.product-checkbox:checked');
        const bulkActions = DOMUtils.$('#bulk-actions');
        const selectedCount = DOMUtils.$('#selected-count');

        if (selectedCheckboxes.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${selectedCheckboxes.length} منتج محدد`;
        } else {
            bulkActions.style.display = 'none';
        }

        // تحديث حالة "تحديد الكل"
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const allCheckboxes = DOMUtils.$$('.product-checkbox');

        if (selectedCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // تبديل حالة المنتجات المحددة
    bulkToggleStatus() {
        const selectedIds = Array.from(DOMUtils.$$('.product-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي منتج', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد العملية',
            `هل تريد تبديل حالة ${selectedIds.length} منتج؟`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    const product = this.products.find(p => p.id === id);
                    if (product) {
                        if (db.updateProduct(id, { active: !product.active })) {
                            successCount++;
                        }
                    }
                });

                app.showNotification(`تم تحديث ${successCount} منتج`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // تحديث فئة المنتجات المحددة
    bulkUpdateCategory() {
        const selectedIds = Array.from(DOMUtils.$$('.product-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي منتج', 'warning');
            return;
        }

        // إظهار نافذة اختيار الفئة
        const categoryOptions = this.categories.map(cat =>
            `<option value="${cat.id}">${cat.name}</option>`
        ).join('');

        const modalHTML = `
            <div id="bulk-category-modal" class="modal show">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تغيير فئة المنتجات</h3>
                        <button class="close-btn" onclick="closeModal('bulk-category-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>اختر الفئة الجديدة لـ ${selectedIds.length} منتج:</p>
                        <select id="new-category" class="form-control">
                            <option value="">اختر الفئة</option>
                            ${categoryOptions}
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('bulk-category-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="productsManager.executeBulkCategoryUpdate('${selectedIds.join(',')}')">تحديث</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // تنفيذ تحديث الفئة المجمع
    executeBulkCategoryUpdate(selectedIdsStr) {
        const selectedIds = selectedIdsStr.split(',');
        const newCategoryId = DOMUtils.$('#new-category')?.value;

        if (!newCategoryId) {
            app.showNotification('يرجى اختيار فئة', 'warning');
            return;
        }

        let successCount = 0;

        selectedIds.forEach(id => {
            if (db.updateProduct(id, { categoryId: newCategoryId })) {
                successCount++;
            }
        });

        app.showNotification(`تم تحديث فئة ${successCount} منتج`, 'success');
        app.closeModal('bulk-category-modal');

        // إزالة النافذة من DOM
        const modal = DOMUtils.$('#bulk-category-modal');
        if (modal) {
            modal.remove();
        }

        this.loadData();
        this.updateTable();
    }

    // حذف المنتجات المحددة
    bulkDelete() {
        const selectedIds = Array.from(DOMUtils.$$('.product-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي منتج', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف ${selectedIds.length} منتج؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    if (db.deleteProduct(id)) {
                        successCount++;
                    }
                });

                app.showNotification(`تم حذف ${successCount} منتج`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // إظهار نافذة الاستيراد
    showImportModal() {
        DOMUtils.addClass('#import-modal', 'show');
    }

    // استيراد المنتجات
    importProducts() {
        const fileInput = DOMUtils.$('#import-file');
        const updateExisting = DOMUtils.$('#update-existing')?.checked;

        if (!fileInput?.files || fileInput.files.length === 0) {
            app.showNotification('يرجى اختيار ملف للاستيراد', 'warning');
            return;
        }

        const file = fileInput.files[0];
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (!['csv', 'xlsx', 'xls'].includes(fileExtension)) {
            app.showNotification('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو Excel', 'error');
            return;
        }

        // هنا يمكن إضافة منطق قراءة الملف ومعالجته
        app.showNotification('ميزة الاستيراد قيد التطوير', 'info');
        app.closeModal('import-modal');
    }

    // تصدير المنتجات
    exportProducts() {
        const data = this.filteredProducts.map(product => {
            const category = this.categories.find(c => c.id === product.categoryId);
            return {
                'الاسم': product.name,
                'الفئة': category ? category.name : '',
                'السعر': product.price,
                'الكمية': product.quantity,
                'الحد الأدنى': product.minStock || 10,
                'الوحدة': product.unit || 'قطعة',
                'الباركود': product.barcode || '',
                'الوصف': product.description || '',
                'تاريخ الصلاحية': product.expiryDate || '',
                'الحالة': product.active ? 'نشط' : 'غير نشط',
                'تاريخ الإضافة': DateUtils.formatDate(product.createdAt)
            };
        });

        const csvContent = this.convertToCSV(data);
        const filename = `products-${DateUtils.getCurrentDate()}.csv`;

        Utils.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
        app.showNotification('تم تصدير المنتجات بنجاح', 'success');
    }

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [];

        // إضافة العناوين
        csvRows.push(headers.join(','));

        // إضافة البيانات
        data.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }
}

// إنشاء مثيل من مدير المنتجات
const productsManager = new ProductsManager();

// تصدير مدير المنتجات للاستخدام العام
window.productsManager = productsManager;