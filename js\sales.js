/**
 * Cybernet Magasin - نظام المبيعات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class SalesSystem {
    constructor() {
        this.currentSale = {
            items: [],
            customer: null,
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            paymentMethod: 'cash'
        };
        this.products = [];
        this.customers = [];
        this.settings = {};
        this.init();
    }

    // تهيئة نظام المبيعات
    init() {
        this.loadData();
        this.resetSale();
    }

    // تحميل البيانات
    loadData() {
        this.products = db.getProducts();
        this.customers = db.getCustomers();
        this.settings = db.getSettings();
    }

    // إعادة تعيين عملية البيع
    resetSale() {
        this.currentSale = {
            items: [],
            customer: this.customers.find(c => c.isDefault) || null,
            subtotal: 0,
            tax: 0,
            discount: 0,
            total: 0,
            paymentMethod: 'cash',
            notes: ''
        };
        this.calculateTotals();
    }

    // عرض واجهة المبيعات
    render() {
        const container = DOMUtils.$('#sales-page');
        if (!container) return;

        this.loadData();

        container.innerHTML = `
            <div class="sales-container">
                <!-- شريط البحث والأدوات -->
                <div class="sales-toolbar">
                    <div class="search-section">
                        <div class="search-box">
                            <input type="text" id="product-search" placeholder="ابحث عن منتج (الاسم، الباركود...)" 
                                   autocomplete="off">
                            <button class="search-btn" onclick="salesSystem.searchProducts()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="quick-actions">
                            <button class="btn btn-secondary" onclick="salesManager.scanBarcode()">
                                <i class="fas fa-barcode"></i>
                                مسح باركود
                            </button>
                            <button class="btn btn-info" onclick="salesManager.showProductsList()">
                                <i class="fas fa-list"></i>
                                قائمة المنتجات
                            </button>
                            <button class="btn btn-warning" onclick="salesManager.showPrintSettings()">
                                <i class="fas fa-print"></i>
                                إعدادات الطباعة
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sales-content">
                    <!-- قائمة المنتجات -->
                    <div class="products-panel">
                        <div class="panel-header">
                            <h3>المنتجات</h3>
                            <div class="category-filter">
                                <select id="category-filter" onchange="salesSystem.filterByCategory()">
                                    <option value="">جميع الفئات</option>
                                    ${this.renderCategoryOptions()}
                                </select>
                            </div>
                        </div>
                        <div class="products-grid" id="products-grid">
                            ${this.renderProductsGrid()}
                        </div>
                    </div>

                    <!-- سلة المشتريات -->
                    <div class="cart-panel">
                        <div class="panel-header">
                            <h3>سلة المشتريات</h3>
                            <button class="btn btn-sm btn-secondary" onclick="salesSystem.clearCart()">
                                <i class="fas fa-trash"></i>
                                مسح الكل
                            </button>
                        </div>

                        <!-- معلومات العميل -->
                        <div class="customer-section">
                            <label>العميل:</label>
                            <select id="customer-select" onchange="salesSystem.selectCustomer()">
                                ${this.renderCustomerOptions()}
                            </select>
                            <button class="btn btn-sm btn-primary" onclick="salesSystem.addNewCustomer()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>

                        <!-- عناصر السلة -->
                        <div class="cart-items" id="cart-items">
                            ${this.renderCartItems()}
                        </div>

                        <!-- الإجماليات -->
                        <div class="cart-totals">
                            <div class="total-row">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">${NumberUtils.formatCurrency(this.currentSale.subtotal)}</span>
                            </div>
                            <div class="total-row">
                                <span>الخصم:</span>
                                <div class="discount-input">
                                    <input type="number" id="discount-amount" value="${this.currentSale.discount}" 
                                           onchange="salesSystem.applyDiscount()" min="0" step="0.01">
                                    <span>دج</span>
                                </div>
                            </div>
                            <div class="total-row">
                                <span>الضريبة (${this.settings.system?.taxRate || 19}%):</span>
                                <span id="tax-amount">${NumberUtils.formatCurrency(this.currentSale.tax)}</span>
                            </div>
                            <div class="total-row grand-total">
                                <span>الإجمالي:</span>
                                <span id="grand-total">${NumberUtils.formatCurrency(this.currentSale.total)}</span>
                            </div>
                        </div>

                        <!-- طريقة الدفع -->
                        <div class="payment-section">
                            <label>طريقة الدفع:</label>
                            <div class="payment-methods">
                                <label class="payment-option">
                                    <input type="radio" name="payment-method" value="cash" 
                                           ${this.currentSale.paymentMethod === 'cash' ? 'checked' : ''}
                                           onchange="salesSystem.selectPaymentMethod('cash')">
                                    <span>نقداً</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="payment-method" value="credit" 
                                           ${this.currentSale.paymentMethod === 'credit' ? 'checked' : ''}
                                           onchange="salesSystem.selectPaymentMethod('credit')">
                                    <span>على الحساب</span>
                                </label>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="notes-section">
                            <label>ملاحظات:</label>
                            <textarea id="sale-notes" placeholder="ملاحظات إضافية..." 
                                      onchange="salesSystem.updateNotes()">${this.currentSale.notes}</textarea>
                        </div>

                        <!-- أزرار العمليات -->
                        <div class="action-buttons">
                            <button class="btn btn-success btn-lg" onclick="salesSystem.completeSale()" 
                                    ${this.currentSale.items.length === 0 ? 'disabled' : ''}>
                                <i class="fas fa-check"></i>
                                إتمام البيع
                            </button>
                            <button class="btn btn-secondary" onclick="salesSystem.holdSale()">
                                <i class="fas fa-pause"></i>
                                تعليق
                            </button>
                            <button class="btn btn-info" onclick="salesSystem.previewInvoice()">
                                <i class="fas fa-eye"></i>
                                معاينة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة قائمة المنتجات -->
            <div id="products-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>اختيار منتج</h3>
                        <button class="close-btn" onclick="closeModal('products-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="products-table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>السعر</th>
                                        <th>الكمية المتاحة</th>
                                        <th>الفئة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="products-table-body">
                                    ${this.renderProductsTable()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة عميل جديد -->
            <div id="new-customer-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إضافة عميل جديد</h3>
                        <button class="close-btn" onclick="closeModal('new-customer-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="new-customer-form">
                            <div class="form-group">
                                <label>اسم العميل *</label>
                                <input type="text" id="customer-name" required>
                            </div>
                            <div class="form-group">
                                <label>رقم الهاتف</label>
                                <input type="tel" id="customer-phone">
                            </div>
                            <div class="form-group">
                                <label>العنوان</label>
                                <textarea id="customer-address"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('new-customer-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="salesSystem.saveNewCustomer()">حفظ</button>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // البحث في المنتجات
        const searchInput = DOMUtils.$('#product-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchProducts(e.target.value);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchProducts(e.target.value);
                }
            });
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (window.app.currentPage === 'sales') {
                switch (e.key) {
                    case 'F1':
                        e.preventDefault();
                        this.showProductsList();
                        break;
                    case 'F2':
                        e.preventDefault();
                        this.completeSale();
                        break;
                    case 'F3':
                        e.preventDefault();
                        this.clearCart();
                        break;
                    case 'F4':
                        e.preventDefault();
                        searchInput?.focus();
                        break;
                }
            }
        });
    }

    // عرض خيارات الفئات
    renderCategoryOptions() {
        const categories = db.getCategories();
        return categories.map(category => 
            `<option value="${category.id}">${category.name}</option>`
        ).join('');
    }

    // عرض شبكة المنتجات
    renderProductsGrid() {
        const activeProducts = this.products.filter(product => 
            product.active && product.quantity > 0
        );

        if (activeProducts.length === 0) {
            return '<div class="no-products">لا توجد منتجات متاحة</div>';
        }

        return activeProducts.map(product => `
            <div class="product-card" onclick="salesSystem.addToCart('${product.id}')">
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.name}">` : 
                        '<i class="fas fa-box"></i>'
                    }
                </div>
                <div class="product-info">
                    <h4>${product.name}</h4>
                    <p class="product-price">${NumberUtils.formatCurrency(product.price)}</p>
                    <p class="product-stock">متوفر: ${product.quantity}</p>
                </div>
            </div>
        `).join('');
    }

    // عرض خيارات العملاء
    renderCustomerOptions() {
        return this.customers.map(customer => 
            `<option value="${customer.id}" ${customer.id === this.currentSale.customer?.id ? 'selected' : ''}>
                ${customer.name}
            </option>`
        ).join('');
    }

    // عرض عناصر السلة
    renderCartItems() {
        if (this.currentSale.items.length === 0) {
            return '<div class="empty-cart">السلة فارغة</div>';
        }

        return this.currentSale.items.map((item, index) => `
            <div class="cart-item">
                <div class="item-info">
                    <h5>${item.name}</h5>
                    <p>${NumberUtils.formatCurrency(item.price)} × ${item.quantity}</p>
                </div>
                <div class="item-controls">
                    <div class="quantity-controls">
                        <button onclick="salesSystem.decreaseQuantity(${index})">-</button>
                        <span>${item.quantity}</span>
                        <button onclick="salesSystem.increaseQuantity(${index})">+</button>
                    </div>
                    <div class="item-total">${NumberUtils.formatCurrency(item.total)}</div>
                    <button class="remove-btn" onclick="salesSystem.removeFromCart(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // عرض جدول المنتجات
    renderProductsTable() {
        return this.products.filter(p => p.active).map(product => `
            <tr>
                <td>${product.name}</td>
                <td>${NumberUtils.formatCurrency(product.price)}</td>
                <td>${product.quantity}</td>
                <td>${this.getCategoryName(product.categoryId)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="salesSystem.addToCart('${product.id}')">
                        إضافة
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // الحصول على اسم الفئة
    getCategoryName(categoryId) {
        const categories = db.getCategories();
        const category = categories.find(c => c.id === categoryId);
        return category ? category.name : 'غير محدد';
    }

    // البحث في المنتجات
    searchProducts(query = '') {
        const searchInput = DOMUtils.$('#product-search');
        const searchQuery = query || searchInput?.value || '';
        
        if (searchQuery.trim() === '') {
            this.updateProductsGrid(this.products.filter(p => p.active));
            return;
        }

        const results = db.searchProducts(searchQuery);
        this.updateProductsGrid(results.filter(p => p.active));
    }

    // تحديث شبكة المنتجات
    updateProductsGrid(products) {
        const grid = DOMUtils.$('#products-grid');
        if (grid) {
            const tempProducts = this.products;
            this.products = products;
            grid.innerHTML = this.renderProductsGrid();
            this.products = tempProducts;
        }
    }

    // فلترة حسب الفئة
    filterByCategory() {
        const categorySelect = DOMUtils.$('#category-filter');
        const categoryId = categorySelect?.value;
        
        if (!categoryId) {
            this.updateProductsGrid(this.products.filter(p => p.active));
            return;
        }

        const filteredProducts = this.products.filter(product => 
            product.active && product.categoryId == categoryId
        );
        
        this.updateProductsGrid(filteredProducts);
    }

    // إضافة منتج للسلة
    addToCart(productId, quantity = 1) {
        const product = this.products.find(p => p.id === productId);
        if (!product) {
            app.showNotification('المنتج غير موجود', 'error');
            return;
        }

        if (product.quantity < quantity) {
            app.showNotification('الكمية المطلوبة غير متوفرة', 'warning');
            return;
        }

        // البحث عن المنتج في السلة
        const existingItemIndex = this.currentSale.items.findIndex(item => item.productId === productId);
        
        if (existingItemIndex !== -1) {
            // زيادة الكمية
            const newQuantity = this.currentSale.items[existingItemIndex].quantity + quantity;
            if (newQuantity > product.quantity) {
                app.showNotification('الكمية المطلوبة تتجاوز المتوفر', 'warning');
                return;
            }
            this.currentSale.items[existingItemIndex].quantity = newQuantity;
            this.currentSale.items[existingItemIndex].total = 
                this.currentSale.items[existingItemIndex].price * newQuantity;
        } else {
            // إضافة منتج جديد
            this.currentSale.items.push({
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity: quantity,
                total: product.price * quantity
            });
        }

        this.calculateTotals();
        this.updateCartDisplay();
        app.showNotification(`تم إضافة ${product.name} للسلة`, 'success');
    }

    // زيادة الكمية
    increaseQuantity(index) {
        const item = this.currentSale.items[index];
        const product = this.products.find(p => p.id === item.productId);
        
        if (item.quantity >= product.quantity) {
            app.showNotification('الكمية المطلوبة تتجاوز المتوفر', 'warning');
            return;
        }

        item.quantity++;
        item.total = item.price * item.quantity;
        this.calculateTotals();
        this.updateCartDisplay();
    }

    // تقليل الكمية
    decreaseQuantity(index) {
        const item = this.currentSale.items[index];
        
        if (item.quantity <= 1) {
            this.removeFromCart(index);
            return;
        }

        item.quantity--;
        item.total = item.price * item.quantity;
        this.calculateTotals();
        this.updateCartDisplay();
    }

    // إزالة من السلة
    removeFromCart(index) {
        this.currentSale.items.splice(index, 1);
        this.calculateTotals();
        this.updateCartDisplay();
    }

    // مسح السلة
    clearCart() {
        app.showConfirm('تأكيد المسح', 'هل تريد مسح جميع العناصر من السلة؟', () => {
            this.currentSale.items = [];
            this.calculateTotals();
            this.updateCartDisplay();
            app.showNotification('تم مسح السلة', 'info');
        });
    }

    // حساب الإجماليات
    calculateTotals() {
        this.currentSale.subtotal = this.currentSale.items.reduce((sum, item) => sum + item.total, 0);
        
        const discountedSubtotal = this.currentSale.subtotal - this.currentSale.discount;
        const taxRate = this.settings.system?.taxRate || 19;
        this.currentSale.tax = (discountedSubtotal * taxRate) / 100;
        this.currentSale.total = discountedSubtotal + this.currentSale.tax;
    }

    // تطبيق الخصم
    applyDiscount() {
        const discountInput = DOMUtils.$('#discount-amount');
        const discount = parseFloat(discountInput?.value || 0);
        
        if (discount < 0) {
            app.showNotification('قيمة الخصم لا يمكن أن تكون سالبة', 'error');
            discountInput.value = 0;
            return;
        }

        if (discount > this.currentSale.subtotal) {
            app.showNotification('قيمة الخصم لا يمكن أن تتجاوز المجموع الفرعي', 'error');
            discountInput.value = this.currentSale.subtotal;
            this.currentSale.discount = this.currentSale.subtotal;
        } else {
            this.currentSale.discount = discount;
        }

        this.calculateTotals();
        this.updateTotalsDisplay();
    }

    // اختيار طريقة الدفع
    selectPaymentMethod(method) {
        this.currentSale.paymentMethod = method;
    }

    // اختيار العميل
    selectCustomer() {
        const customerSelect = DOMUtils.$('#customer-select');
        const customerId = customerSelect?.value;
        this.currentSale.customer = this.customers.find(c => c.id === customerId);
    }

    // تحديث الملاحظات
    updateNotes() {
        const notesTextarea = DOMUtils.$('#sale-notes');
        this.currentSale.notes = notesTextarea?.value || '';
    }

    // تحديث عرض السلة
    updateCartDisplay() {
        const cartItems = DOMUtils.$('#cart-items');
        if (cartItems) {
            cartItems.innerHTML = this.renderCartItems();
        }
        this.updateTotalsDisplay();
        this.updateActionButtons();
    }

    // تحديث عرض الإجماليات
    updateTotalsDisplay() {
        const subtotalElement = DOMUtils.$('#subtotal');
        const taxElement = DOMUtils.$('#tax-amount');
        const totalElement = DOMUtils.$('#grand-total');
        
        if (subtotalElement) subtotalElement.textContent = NumberUtils.formatCurrency(this.currentSale.subtotal);
        if (taxElement) taxElement.textContent = NumberUtils.formatCurrency(this.currentSale.tax);
        if (totalElement) totalElement.textContent = NumberUtils.formatCurrency(this.currentSale.total);
    }

    // تحديث أزرار العمليات
    updateActionButtons() {
        const completeBtn = DOMUtils.$('.action-buttons .btn-success');
        if (completeBtn) {
            completeBtn.disabled = this.currentSale.items.length === 0;
        }
    }

    // إظهار قائمة المنتجات
    showProductsList() {
        const modal = DOMUtils.$('#products-modal');
        const tableBody = DOMUtils.$('#products-table-body');
        
        if (tableBody) {
            tableBody.innerHTML = this.renderProductsTable();
        }
        
        DOMUtils.addClass(modal, 'show');
    }

    // إضافة عميل جديد
    addNewCustomer() {
        DOMUtils.addClass('#new-customer-modal', 'show');
        setTimeout(() => {
            DOMUtils.$('#customer-name')?.focus();
        }, 100);
    }

    // حفظ عميل جديد
    saveNewCustomer() {
        const name = DOMUtils.$('#customer-name')?.value?.trim();
        const phone = DOMUtils.$('#customer-phone')?.value?.trim();
        const address = DOMUtils.$('#customer-address')?.value?.trim();
        
        if (!name) {
            app.showNotification('اسم العميل مطلوب', 'error');
            return;
        }

        const newCustomer = {
            name: name,
            phone: phone || '',
            email: '',
            address: address || '',
            balance: 0,
            creditLimit: 0,
            active: true
        };

        if (db.addCustomer(newCustomer)) {
            this.loadData();
            
            // تحديث قائمة العملاء
            const customerSelect = DOMUtils.$('#customer-select');
            if (customerSelect) {
                customerSelect.innerHTML = this.renderCustomerOptions();
                customerSelect.value = newCustomer.id;
                this.selectCustomer();
            }
            
            app.showNotification('تم إضافة العميل بنجاح', 'success');
            app.closeModal('new-customer-modal');
            
            // مسح النموذج
            DOMUtils.$('#new-customer-form')?.reset();
        } else {
            app.showNotification('فشل في إضافة العميل', 'error');
        }
    }

    // مسح الباركود
    scanBarcode() {
        // يمكن إضافة منطق مسح الباركود هنا
        app.showNotification('ميزة مسح الباركود قيد التطوير', 'info');
    }

    // معاينة الفاتورة
    previewInvoice() {
        if (this.currentSale.items.length === 0) {
            app.showNotification('السلة فارغة', 'warning');
            return;
        }

        // يمكن إضافة منطق معاينة الفاتورة هنا
        app.showNotification('معاينة الفاتورة قيد التطوير', 'info');
    }

    // تعليق البيع
    holdSale() {
        if (this.currentSale.items.length === 0) {
            app.showNotification('السلة فارغة', 'warning');
            return;
        }

        // حفظ البيع المعلق
        const heldSales = StorageUtils.get('held_sales', []);
        const heldSale = {
            id: StringUtils.generateId(),
            ...this.currentSale,
            heldAt: new Date().toISOString()
        };
        
        heldSales.push(heldSale);
        StorageUtils.set('held_sales', heldSales);
        
        this.resetSale();
        this.render();
        app.showNotification('تم تعليق البيع', 'info');
    }

    // إتمام البيع
    completeSale() {
        if (this.currentSale.items.length === 0) {
            app.showNotification('السلة فارغة', 'warning');
            return;
        }

        // التحقق من توفر المنتجات
        for (const item of this.currentSale.items) {
            const product = this.products.find(p => p.id === item.productId);
            if (!product || product.quantity < item.quantity) {
                app.showNotification(`المنتج ${item.name} غير متوفر بالكمية المطلوبة`, 'error');
                return;
            }
        }

        const sale = {
            customerId: this.currentSale.customer?.id,
            items: [...this.currentSale.items],
            subtotal: this.currentSale.subtotal,
            discount: this.currentSale.discount,
            tax: this.currentSale.tax,
            total: this.currentSale.total,
            paymentMethod: this.currentSale.paymentMethod,
            notes: this.currentSale.notes
        };

        if (db.addSale(sale)) {
            app.showNotification('تم إتمام البيع بنجاح', 'success');
            
            // طباعة الفاتورة
            if (window.printSystem) {
                window.printSystem.printInvoice(sale);
            }
            
            this.resetSale();
            this.render();
            
            // تحديث لوحة المعلومات
            if (window.dashboard) {
                window.dashboard.updateStats();
            }
        } else {
            app.showNotification('فشل في حفظ البيع', 'error');
        }
    }
}

// إنشاء مثيل من نظام المبيعات
const salesSystem = new SalesSystem();

// تصدير نظام المبيعات للاستخدام العام
window.salesSystem = salesSystem;
