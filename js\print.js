/**
 * Cybernet Magasin - نظام الطباعة
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class PrintSystem {
    constructor() {
        this.settings = {};
        this.init();
    }

    // تهيئة نظام الطباعة
    init() {
        this.loadSettings();
    }

    // تحميل الإعدادات
    loadSettings() {
        this.settings = db.getSettings();
    }

    // طباعة فاتورة البيع
    printInvoice(sale) {
        this.loadSettings();
        
        const customer = db.getCustomers().find(c => c.id === sale.customerId) || { name: 'ضيف' };
        const invoiceHtml = this.generateInvoiceHtml(sale, customer);
        
        this.printDocument(invoiceHtml, `فاتورة-${sale.saleNumber}`);
    }

    // طباعة فاتورة الشراء
    printPurchaseInvoice(purchase) {
        this.loadSettings();
        
        const supplier = db.getSuppliers().find(s => s.id === purchase.supplierId) || { name: 'غير محدد' };
        const invoiceHtml = this.generatePurchaseInvoiceHtml(purchase, supplier);
        
        this.printDocument(invoiceHtml, `فاتورة-شراء-${purchase.purchaseNumber}`);
    }

    // توليد HTML فاتورة البيع
    generateInvoiceHtml(sale, customer) {
        const company = this.settings.company || {};
        const system = this.settings.system || {};
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة بيع #${sale.saleNumber}</title>
                <style>
                    ${this.getInvoiceStyles()}
                </style>
            </head>
            <body>
                <div class="invoice">
                    <!-- رأس الفاتورة -->
                    <div class="invoice-header">
                        <div class="company-info">
                            ${company.logo ? `<img src="${company.logo}" alt="شعار الشركة" class="company-logo">` : ''}
                            <div class="company-details">
                                <h1>${company.name || 'اسم الشركة'}</h1>
                                ${company.tradeName ? `<p class="trade-name">${company.tradeName}</p>` : ''}
                                ${company.address ? `<p>${company.address}</p>` : ''}
                                <div class="contact-info">
                                    ${company.phone ? `<span>الهاتف: ${company.phone}</span>` : ''}
                                    ${company.email ? `<span>البريد: ${company.email}</span>` : ''}
                                </div>
                                ${company.taxNumber ? `<p>الرقم الضريبي: ${company.taxNumber}</p>` : ''}
                                ${company.commercialRegister ? `<p>السجل التجاري: ${company.commercialRegister}</p>` : ''}
                            </div>
                        </div>
                        <div class="invoice-info">
                            <h2>فاتورة بيع</h2>
                            <table class="info-table">
                                <tr>
                                    <td>رقم الفاتورة:</td>
                                    <td><strong>${sale.saleNumber}</strong></td>
                                </tr>
                                <tr>
                                    <td>التاريخ:</td>
                                    <td>${DateUtils.formatDate(sale.createdAt, system.dateFormat || 'dd/mm/yyyy')}</td>
                                </tr>
                                <tr>
                                    <td>الوقت:</td>
                                    <td>${DateUtils.formatTime(sale.createdAt)}</td>
                                </tr>
                                <tr>
                                    <td>طريقة الدفع:</td>
                                    <td>${sale.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- معلومات العميل -->
                    <div class="customer-info">
                        <h3>معلومات العميل</h3>
                        <table class="info-table">
                            <tr>
                                <td>اسم العميل:</td>
                                <td><strong>${customer.name}</strong></td>
                            </tr>
                            ${customer.phone ? `
                                <tr>
                                    <td>الهاتف:</td>
                                    <td>${customer.phone}</td>
                                </tr>
                            ` : ''}
                            ${customer.address ? `
                                <tr>
                                    <td>العنوان:</td>
                                    <td>${customer.address}</td>
                                </tr>
                            ` : ''}
                        </table>
                    </div>

                    <!-- تفاصيل المنتجات -->
                    <div class="items-section">
                        <h3>تفاصيل المنتجات</h3>
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${sale.items.map((item, index) => `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>${item.name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${NumberUtils.formatCurrency(item.price)}</td>
                                        <td>${NumberUtils.formatCurrency(item.total)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <!-- الإجماليات -->
                    <div class="totals-section">
                        <table class="totals-table">
                            <tr>
                                <td>المجموع الفرعي:</td>
                                <td>${NumberUtils.formatCurrency(sale.subtotal)}</td>
                            </tr>
                            ${sale.discount > 0 ? `
                                <tr>
                                    <td>الخصم:</td>
                                    <td>-${NumberUtils.formatCurrency(sale.discount)}</td>
                                </tr>
                            ` : ''}
                            <tr>
                                <td>الضريبة (${system.taxRate || 19}%):</td>
                                <td>${NumberUtils.formatCurrency(sale.tax || 0)}</td>
                            </tr>
                            <tr class="total-row">
                                <td><strong>الإجمالي:</strong></td>
                                <td><strong>${NumberUtils.formatCurrency(sale.total)}</strong></td>
                            </tr>
                        </table>
                    </div>

                    ${sale.notes ? `
                        <div class="notes-section">
                            <h3>ملاحظات</h3>
                            <p>${sale.notes}</p>
                        </div>
                    ` : ''}

                    <!-- تذييل الفاتورة -->
                    <div class="invoice-footer">
                        <div class="footer-text">
                            <p>شكراً لتعاملكم معنا</p>
                            <p>تم إنشاء هذه الفاتورة بواسطة نظام Cybernet Magasin</p>
                        </div>
                        ${sale.paymentMethod === 'cash' ? `
                            <div class="signature-section">
                                <div class="signature-box">
                                    <p>توقيع العميل</p>
                                    <div class="signature-line"></div>
                                </div>
                                <div class="signature-box">
                                    <p>توقيع البائع</p>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    // توليد HTML فاتورة الشراء
    generatePurchaseInvoiceHtml(purchase, supplier) {
        const company = this.settings.company || {};
        const system = this.settings.system || {};
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة شراء #${purchase.purchaseNumber}</title>
                <style>
                    ${this.getInvoiceStyles()}
                </style>
            </head>
            <body>
                <div class="invoice">
                    <!-- رأس الفاتورة -->
                    <div class="invoice-header">
                        <div class="company-info">
                            ${company.logo ? `<img src="${company.logo}" alt="شعار الشركة" class="company-logo">` : ''}
                            <div class="company-details">
                                <h1>${company.name || 'اسم الشركة'}</h1>
                                ${company.tradeName ? `<p class="trade-name">${company.tradeName}</p>` : ''}
                                ${company.address ? `<p>${company.address}</p>` : ''}
                            </div>
                        </div>
                        <div class="invoice-info">
                            <h2>فاتورة شراء</h2>
                            <table class="info-table">
                                <tr>
                                    <td>رقم الشراء:</td>
                                    <td><strong>${purchase.purchaseNumber}</strong></td>
                                </tr>
                                <tr>
                                    <td>التاريخ:</td>
                                    <td>${DateUtils.formatDate(purchase.purchaseDate || purchase.createdAt, system.dateFormat || 'dd/mm/yyyy')}</td>
                                </tr>
                                ${purchase.invoiceNumber ? `
                                    <tr>
                                        <td>رقم فاتورة المورد:</td>
                                        <td>${purchase.invoiceNumber}</td>
                                    </tr>
                                ` : ''}
                                <tr>
                                    <td>الحالة:</td>
                                    <td>${purchase.status === 'received' ? 'مستلم' : purchase.status === 'pending' ? 'في الانتظار' : 'استلام جزئي'}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- معلومات المورد -->
                    <div class="supplier-info">
                        <h3>معلومات المورد</h3>
                        <table class="info-table">
                            <tr>
                                <td>اسم المورد:</td>
                                <td><strong>${supplier.name}</strong></td>
                            </tr>
                            ${supplier.company ? `
                                <tr>
                                    <td>الشركة:</td>
                                    <td>${supplier.company}</td>
                                </tr>
                            ` : ''}
                            ${supplier.phone ? `
                                <tr>
                                    <td>الهاتف:</td>
                                    <td>${supplier.phone}</td>
                                </tr>
                            ` : ''}
                        </table>
                    </div>

                    <!-- تفاصيل المنتجات -->
                    <div class="items-section">
                        <h3>تفاصيل المنتجات</h3>
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المنتج</th>
                                    <th>الكمية</th>
                                    <th>سعر الشراء</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchase.items.map((item, index) => `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>${item.name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${NumberUtils.formatCurrency(item.purchasePrice)}</td>
                                        <td>${NumberUtils.formatCurrency(item.total)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <!-- الإجماليات -->
                    <div class="totals-section">
                        <table class="totals-table">
                            <tr>
                                <td>المجموع الفرعي:</td>
                                <td>${NumberUtils.formatCurrency(purchase.subtotal)}</td>
                            </tr>
                            ${purchase.discount > 0 ? `
                                <tr>
                                    <td>الخصم:</td>
                                    <td>-${NumberUtils.formatCurrency(purchase.discount)}</td>
                                </tr>
                            ` : ''}
                            <tr>
                                <td>الضريبة (${system.taxRate || 19}%):</td>
                                <td>${NumberUtils.formatCurrency(purchase.tax || 0)}</td>
                            </tr>
                            <tr class="total-row">
                                <td><strong>الإجمالي:</strong></td>
                                <td><strong>${NumberUtils.formatCurrency(purchase.total)}</strong></td>
                            </tr>
                        </table>
                    </div>

                    ${purchase.notes ? `
                        <div class="notes-section">
                            <h3>ملاحظات</h3>
                            <p>${purchase.notes}</p>
                        </div>
                    ` : ''}

                    <!-- تذييل الفاتورة -->
                    <div class="invoice-footer">
                        <div class="footer-text">
                            <p>تم إنشاء هذه الفاتورة بواسطة نظام Cybernet Magasin</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    // أنماط CSS للفاتورة
    getInvoiceStyles() {
        return `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #333;
                background: white;
                direction: rtl;
            }

            .invoice {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: white;
            }

            .invoice-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #730C02;
            }

            .company-info {
                flex: 1;
            }

            .company-logo {
                max-width: 100px;
                max-height: 80px;
                margin-bottom: 10px;
            }

            .company-details h1 {
                color: #730C02;
                font-size: 24px;
                margin-bottom: 5px;
            }

            .trade-name {
                font-style: italic;
                color: #666;
                margin-bottom: 10px;
            }

            .contact-info {
                margin: 10px 0;
            }

            .contact-info span {
                display: inline-block;
                margin-left: 20px;
            }

            .invoice-info {
                text-align: center;
                min-width: 250px;
            }

            .invoice-info h2 {
                background: #730C02;
                color: white;
                padding: 10px;
                margin-bottom: 15px;
                border-radius: 5px;
            }

            .info-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }

            .info-table td {
                padding: 8px;
                border: 1px solid #ddd;
                text-align: right;
            }

            .info-table td:first-child {
                background: #f8f9fa;
                font-weight: bold;
                width: 40%;
            }

            .customer-info,
            .supplier-info {
                margin-bottom: 30px;
            }

            .customer-info h3,
            .supplier-info h3 {
                background: #f8f9fa;
                padding: 10px;
                margin-bottom: 15px;
                border-right: 4px solid #730C02;
            }

            .items-section {
                margin-bottom: 30px;
            }

            .items-section h3 {
                background: #f8f9fa;
                padding: 10px;
                margin-bottom: 15px;
                border-right: 4px solid #730C02;
            }

            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }

            .items-table th,
            .items-table td {
                padding: 12px;
                text-align: center;
                border: 1px solid #ddd;
            }

            .items-table th {
                background: #730C02;
                color: white;
                font-weight: bold;
            }

            .items-table tbody tr:nth-child(even) {
                background: #f8f9fa;
            }

            .totals-section {
                margin-bottom: 30px;
            }

            .totals-table {
                width: 100%;
                max-width: 400px;
                margin-right: auto;
                border-collapse: collapse;
            }

            .totals-table td {
                padding: 10px;
                border: 1px solid #ddd;
                text-align: right;
            }

            .totals-table td:first-child {
                background: #f8f9fa;
                font-weight: bold;
                width: 60%;
            }

            .total-row td {
                background: #730C02 !important;
                color: white !important;
                font-size: 16px;
                font-weight: bold;
            }

            .notes-section {
                margin-bottom: 30px;
            }

            .notes-section h3 {
                background: #f8f9fa;
                padding: 10px;
                margin-bottom: 15px;
                border-right: 4px solid #730C02;
            }

            .notes-section p {
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
                line-height: 1.8;
            }

            .invoice-footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
            }

            .footer-text {
                text-align: center;
                color: #666;
                margin-bottom: 30px;
            }

            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 40px;
            }

            .signature-box {
                text-align: center;
                width: 200px;
            }

            .signature-line {
                border-bottom: 2px solid #333;
                margin-top: 30px;
                height: 1px;
            }

            @media print {
                body {
                    font-size: 12px;
                }
                
                .invoice {
                    padding: 0;
                    box-shadow: none;
                }
                
                .invoice-header {
                    page-break-inside: avoid;
                }
                
                .items-table {
                    page-break-inside: auto;
                }
                
                .items-table tr {
                    page-break-inside: avoid;
                }
            }
        `;
    }

    // طباعة المستند
    printDocument(htmlContent, filename = 'document') {
        // إنشاء نافذة جديدة للطباعة
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (printWindow) {
            printWindow.document.write(htmlContent);
            printWindow.document.close();
            
            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };
        } else {
            // في حالة فشل فتح النافذة، استخدم الطباعة المباشرة
            const printFrame = document.createElement('iframe');
            printFrame.style.display = 'none';
            document.body.appendChild(printFrame);
            
            const frameDoc = printFrame.contentDocument || printFrame.contentWindow.document;
            frameDoc.write(htmlContent);
            frameDoc.close();
            
            setTimeout(() => {
                printFrame.contentWindow.print();
                document.body.removeChild(printFrame);
            }, 500);
        }
    }

    // طباعة تقرير
    printReport(reportHtml, title = 'تقرير') {
        const reportContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title}</title>
                <style>
                    ${this.getReportStyles()}
                </style>
            </head>
            <body>
                <div class="report">
                    <div class="report-header">
                        <h1>${title}</h1>
                        <p>تاريخ الطباعة: ${DateUtils.formatDate(new Date(), 'dd/mm/yyyy hh:mm')}</p>
                    </div>
                    <div class="report-content">
                        ${reportHtml}
                    </div>
                </div>
            </body>
            </html>
        `;
        
        this.printDocument(reportContent, title);
    }

    // أنماط CSS للتقارير
    getReportStyles() {
        return `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                line-height: 1.6;
                color: #333;
                background: white;
                direction: rtl;
            }

            .report {
                max-width: 1000px;
                margin: 0 auto;
                padding: 20px;
            }

            .report-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #730C02;
            }

            .report-header h1 {
                color: #730C02;
                font-size: 24px;
                margin-bottom: 10px;
            }

            .report-content table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }

            .report-content th,
            .report-content td {
                padding: 8px;
                text-align: right;
                border: 1px solid #ddd;
            }

            .report-content th {
                background: #730C02;
                color: white;
                font-weight: bold;
            }

            .report-content tbody tr:nth-child(even) {
                background: #f8f9fa;
            }

            @media print {
                .report {
                    padding: 0;
                }
            }
        `;
    }
}

// إنشاء مثيل من نظام الطباعة
const printSystem = new PrintSystem();

// تصدير نظام الطباعة للاستخدام العام
window.printSystem = printSystem;
