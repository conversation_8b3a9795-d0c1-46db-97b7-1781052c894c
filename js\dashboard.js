/**
 * Cybernet Magasin - لوحة المعلومات
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class Dashboard {
    constructor() {
        this.stats = {};
        this.charts = {};
        this.refreshInterval = null;
        this.init();
    }

    // تهيئة لوحة المعلومات
    init() {
        this.loadStats();
        this.setupAutoRefresh();
    }

    // تحميل الإحصائيات
    loadStats() {
        this.stats = db.getQuickStats();
        this.calculateAdditionalStats();
    }

    // حساب إحصائيات إضافية
    calculateAdditionalStats() {
        const sales = db.getSales();
        const products = db.getProducts();
        const customers = db.getCustomers();
        const settings = db.getSettings();
        
        // إحصائيات المبيعات
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);
        
        const monthSales = sales.filter(sale => 
            new Date(sale.createdAt) >= thisMonth
        );
        
        const lastMonth = new Date(thisMonth);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        
        const lastMonthSales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate >= lastMonth && saleDate < thisMonth;
        });
        
        // حساب النمو
        const currentMonthRevenue = monthSales.reduce((sum, sale) => sum + sale.total, 0);
        const lastMonthRevenue = lastMonthSales.reduce((sum, sale) => sum + sale.total, 0);
        const growthRate = lastMonthRevenue > 0 ? 
            ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;
        
        // أفضل المنتجات مبيعاً
        const productSales = {};
        sales.forEach(sale => {
            sale.items.forEach(item => {
                if (!productSales[item.productId]) {
                    productSales[item.productId] = {
                        quantity: 0,
                        revenue: 0,
                        product: products.find(p => p.id === item.productId)
                    };
                }
                productSales[item.productId].quantity += item.quantity;
                productSales[item.productId].revenue += item.total;
            });
        });
        
        const topProducts = Object.values(productSales)
            .filter(item => item.product)
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5);
        
        // العملاء المدينين
        const debtorCustomers = customers.filter(customer => 
            customer.balance > 0 && !customer.isDefault
        );
        
        // المنتجات منتهية الصلاحية (إذا كانت متوفرة)
        const expiredProducts = products.filter(product => 
            product.expiryDate && new Date(product.expiryDate) < new Date()
        );
        
        // إضافة الإحصائيات الجديدة
        this.stats = {
            ...this.stats,
            monthlyRevenue: currentMonthRevenue,
            lastMonthRevenue: lastMonthRevenue,
            growthRate: growthRate,
            topProducts: topProducts,
            debtorCustomers: debtorCustomers,
            totalDebt: debtorCustomers.reduce((sum, customer) => sum + customer.balance, 0),
            expiredProducts: expiredProducts.length,
            lowStockAlert: settings.system?.lowStockAlert || 10
        };
    }

    // عرض لوحة المعلومات
    render() {
        const container = DOMUtils.$('#dashboard-page');
        if (!container) return;
        
        this.loadStats();
        
        container.innerHTML = `
            <div class="dashboard-container">
                <!-- الإحصائيات السريعة -->
                <div class="stats-grid">
                    ${this.renderStatsCards()}
                </div>
                
                <!-- الرسوم البيانية والتقارير -->
                <div class="charts-section">
                    <div class="row">
                        <div class="col-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">مبيعات آخر 7 أيام</h3>
                                </div>
                                <div class="card-body">
                                    <div id="sales-chart" class="chart-container">
                                        ${this.renderSalesChart()}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">أفضل المنتجات</h3>
                                </div>
                                <div class="card-body">
                                    ${this.renderTopProducts()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- التنبيهات والإشعارات -->
                <div class="alerts-section">
                    <div class="row">
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">تنبيهات المخزون</h3>
                                </div>
                                <div class="card-body">
                                    ${this.renderStockAlerts()}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">الديون المستحقة</h3>
                                </div>
                                <div class="card-body">
                                    ${this.renderDebtAlerts()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الأنشطة الأخيرة -->
                <div class="activities-section">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">الأنشطة الأخيرة</h3>
                        </div>
                        <div class="card-body">
                            ${this.renderRecentActivities()}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة مستمعي الأحداث
        this.setupEventListeners();
    }

    // عرض بطاقات الإحصائيات
    renderStatsCards() {
        const cards = [
            {
                title: 'إجمالي المبيعات',
                value: NumberUtils.formatCurrency(this.stats.totalRevenue),
                icon: 'fas fa-chart-line',
                color: 'primary',
                change: this.stats.growthRate,
                changeText: 'مقارنة بالشهر الماضي'
            },
            {
                title: 'مبيعات اليوم',
                value: NumberUtils.formatCurrency(this.stats.todayRevenue),
                icon: 'fas fa-cash-register',
                color: 'success',
                count: this.stats.todaySales,
                countText: 'عملية بيع'
            },
            {
                title: 'إجمالي المنتجات',
                value: this.stats.totalProducts.toString(),
                icon: 'fas fa-boxes',
                color: 'info',
                alert: this.stats.lowStockCount,
                alertText: 'منتج بمخزون منخفض'
            },
            {
                title: 'إجمالي العملاء',
                value: this.stats.totalCustomers.toString(),
                icon: 'fas fa-users',
                color: 'warning',
                debt: this.stats.totalDebt,
                debtText: 'إجمالي الديون'
            }
        ];
        
        return cards.map(card => `
            <div class="stats-card ${card.color}">
                <div class="stats-icon">
                    <i class="${card.icon}"></i>
                </div>
                <div class="stats-content">
                    <h3>${card.value}</h3>
                    <p>${card.title}</p>
                    ${card.change !== undefined ? `
                        <div class="stats-change ${card.change >= 0 ? 'positive' : 'negative'}">
                            <i class="fas fa-arrow-${card.change >= 0 ? 'up' : 'down'}"></i>
                            ${Math.abs(card.change).toFixed(1)}% ${card.changeText}
                        </div>
                    ` : ''}
                    ${card.count !== undefined ? `
                        <div class="stats-detail">
                            <strong>${card.count}</strong> ${card.countText}
                        </div>
                    ` : ''}
                    ${card.alert !== undefined && card.alert > 0 ? `
                        <div class="stats-alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${card.alert} ${card.alertText}
                        </div>
                    ` : ''}
                    ${card.debt !== undefined && card.debt > 0 ? `
                        <div class="stats-debt">
                            <strong>${NumberUtils.formatCurrency(card.debt)}</strong> ${card.debtText}
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    // عرض رسم بياني للمبيعات
    renderSalesChart() {
        const sales = db.getSales();
        const last7Days = [];
        
        // إنشاء بيانات آخر 7 أيام
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            
            const daySales = sales.filter(sale => 
                sale.createdAt.split('T')[0] === dateStr
            );
            
            const dayRevenue = daySales.reduce((sum, sale) => sum + sale.total, 0);
            
            last7Days.push({
                date: DateUtils.formatDate(date, 'dd/mm'),
                revenue: dayRevenue,
                count: daySales.length
            });
        }
        
        const maxRevenue = Math.max(...last7Days.map(day => day.revenue));
        
        return `
            <div class="simple-chart">
                ${last7Days.map(day => `
                    <div class="chart-bar">
                        <div class="bar" style="height: ${maxRevenue > 0 ? (day.revenue / maxRevenue) * 100 : 0}%">
                            <div class="bar-tooltip">
                                <strong>${NumberUtils.formatCurrency(day.revenue)}</strong><br>
                                ${day.count} عملية بيع
                            </div>
                        </div>
                        <div class="bar-label">${day.date}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // عرض أفضل المنتجات
    renderTopProducts() {
        if (!this.stats.topProducts || this.stats.topProducts.length === 0) {
            return '<p class="text-center text-secondary">لا توجد بيانات مبيعات</p>';
        }
        
        return `
            <div class="top-products-list">
                ${this.stats.topProducts.map((item, index) => `
                    <div class="product-item">
                        <div class="product-rank">${index + 1}</div>
                        <div class="product-info">
                            <h4>${item.product.name}</h4>
                            <p>الكمية: ${item.quantity} | الإيرادات: ${NumberUtils.formatCurrency(item.revenue)}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // عرض تنبيهات المخزون
    renderStockAlerts() {
        const products = db.getProducts();
        const lowStockProducts = products.filter(product => 
            product.quantity <= this.stats.lowStockAlert && product.active
        );
        
        if (lowStockProducts.length === 0) {
            return '<p class="text-center text-success">جميع المنتجات بمخزون كافي</p>';
        }
        
        return `
            <div class="stock-alerts">
                ${lowStockProducts.slice(0, 5).map(product => `
                    <div class="alert-item">
                        <div class="alert-icon">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                        <div class="alert-content">
                            <h5>${product.name}</h5>
                            <p>الكمية المتبقية: <strong>${product.quantity}</strong></p>
                        </div>
                    </div>
                `).join('')}
                ${lowStockProducts.length > 5 ? `
                    <div class="alert-more">
                        <a href="#" onclick="showPage('products')">
                            عرض جميع المنتجات (${lowStockProducts.length})
                        </a>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // عرض تنبيهات الديون
    renderDebtAlerts() {
        if (!this.stats.debtorCustomers || this.stats.debtorCustomers.length === 0) {
            return '<p class="text-center text-success">لا توجد ديون مستحقة</p>';
        }
        
        return `
            <div class="debt-alerts">
                ${this.stats.debtorCustomers.slice(0, 5).map(customer => `
                    <div class="alert-item">
                        <div class="alert-icon">
                            <i class="fas fa-user-clock text-danger"></i>
                        </div>
                        <div class="alert-content">
                            <h5>${customer.name}</h5>
                            <p>المبلغ المستحق: <strong>${NumberUtils.formatCurrency(customer.balance)}</strong></p>
                        </div>
                    </div>
                `).join('')}
                ${this.stats.debtorCustomers.length > 5 ? `
                    <div class="alert-more">
                        <a href="#" onclick="showPage('customers')">
                            عرض جميع العملاء المدينين (${this.stats.debtorCustomers.length})
                        </a>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // عرض الأنشطة الأخيرة
    renderRecentActivities() {
        const sales = db.getSales();
        const recentSales = sales
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 10);
        
        if (recentSales.length === 0) {
            return '<p class="text-center text-secondary">لا توجد أنشطة حديثة</p>';
        }
        
        return `
            <div class="activities-list">
                ${recentSales.map(sale => {
                    const customer = db.getCustomers().find(c => c.id === sale.customerId);
                    return `
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-shopping-cart text-success"></i>
                            </div>
                            <div class="activity-content">
                                <h5>عملية بيع #${sale.saleNumber}</h5>
                                <p>
                                    العميل: ${customer ? customer.name : 'غير محدد'} | 
                                    المبلغ: ${NumberUtils.formatCurrency(sale.total)} |
                                    ${DateUtils.formatDate(sale.createdAt, 'dd/mm/yyyy hh:mm')}
                                </p>
                            </div>
                            <div class="activity-actions">
                                <button class="btn btn-sm btn-secondary" onclick="viewSale('${sale.id}')">
                                    عرض
                                </button>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تحديث الإحصائيات
        window.updateDashboard = () => {
            this.updateStats();
        };
        
        // عرض عملية بيع
        window.viewSale = (saleId) => {
            this.viewSale(saleId);
        };
    }

    // تحديث الإحصائيات
    updateStats() {
        this.loadStats();
        this.render();
    }

    // عرض عملية بيع
    viewSale(saleId) {
        const sale = db.getSales().find(s => s.id === saleId);
        if (sale && window.salesSystem) {
            window.salesSystem.viewSale(sale);
        }
    }

    // إعداد التحديث التلقائي
    setupAutoRefresh() {
        // تحديث كل 5 دقائق
        this.refreshInterval = setInterval(() => {
            if (window.app && window.app.currentPage === 'dashboard') {
                this.updateStats();
            }
        }, 5 * 60 * 1000);
    }

    // إيقاف التحديث التلقائي
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // تنظيف الموارد
    destroy() {
        this.stopAutoRefresh();
    }
}

// إنشاء مثيل من لوحة المعلومات
const dashboard = new Dashboard();

// تصدير لوحة المعلومات للاستخدام العام
window.dashboard = dashboard;
