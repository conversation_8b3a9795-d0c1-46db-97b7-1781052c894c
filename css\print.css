/* 
 * Cybernet Magasin - أنماط الطباعة
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

@media print {
    /* إعدادات عامة للطباعة */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    @page {
        size: A4;
        margin: 1cm;
        direction: rtl;
    }
    
    body {
        font-family: 'Cairo', Arial, sans-serif !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: #fff !important;
        direction: rtl !important;
        text-align: right !important;
    }
    
    /* إخفاء العناصر غير المطلوبة */
    .sidebar,
    .top-bar,
    .loading-screen,
    .login-screen,
    .modal,
    .btn,
    .sidebar-toggle,
    .theme-toggle,
    .notifications,
    .user-menu,
    .no-print {
        display: none !important;
    }
    
    /* تعديل تخطيط المحتوى */
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: none !important;
    }
    
    .page {
        display: block !important;
        page-break-after: auto;
    }
    
    /* تنسيق الفواتير */
    .invoice {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 20pt;
        background: #fff !important;
        border: none !important;
        box-shadow: none !important;
    }
    
    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20pt;
        padding-bottom: 15pt;
        border-bottom: 2pt solid #730C02;
    }
    
    .company-info {
        text-align: right;
    }
    
    .company-info h1 {
        font-size: 18pt;
        font-weight: bold;
        color: #730C02 !important;
        margin-bottom: 5pt;
    }
    
    .company-info p {
        font-size: 10pt;
        margin: 2pt 0;
        color: #333 !important;
    }
    
    .invoice-info {
        text-align: left;
    }
    
    .invoice-info h2 {
        font-size: 16pt;
        font-weight: bold;
        color: #730C02 !important;
        margin-bottom: 10pt;
    }
    
    .invoice-info p {
        font-size: 10pt;
        margin: 2pt 0;
        color: #333 !important;
    }
    
    /* معلومات العميل */
    .customer-info {
        margin-bottom: 20pt;
        padding: 10pt;
        background: #f8f9fa !important;
        border: 1pt solid #dee2e6;
    }
    
    .customer-info h3 {
        font-size: 12pt;
        font-weight: bold;
        color: #730C02 !important;
        margin-bottom: 8pt;
    }
    
    .customer-info p {
        font-size: 10pt;
        margin: 2pt 0;
        color: #333 !important;
    }
    
    /* جدول المنتجات */
    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20pt;
        font-size: 10pt;
    }
    
    .invoice-table th {
        background: #730C02 !important;
        color: #fff !important;
        padding: 8pt;
        text-align: center;
        font-weight: bold;
        border: 1pt solid #730C02;
    }
    
    .invoice-table td {
        padding: 6pt 8pt;
        text-align: center;
        border: 1pt solid #dee2e6;
        color: #333 !important;
    }
    
    .invoice-table tbody tr:nth-child(even) {
        background: #f8f9fa !important;
    }
    
    .invoice-table .text-right {
        text-align: right !important;
    }
    
    .invoice-table .text-left {
        text-align: left !important;
    }
    
    /* إجمالي الفاتورة */
    .invoice-total {
        margin-top: 20pt;
        text-align: left;
    }
    
    .total-row {
        display: flex;
        justify-content: space-between;
        padding: 5pt 0;
        font-size: 11pt;
    }
    
    .total-row.grand-total {
        font-size: 14pt;
        font-weight: bold;
        color: #730C02 !important;
        border-top: 2pt solid #730C02;
        padding-top: 10pt;
        margin-top: 10pt;
    }
    
    /* ملاحظات الفاتورة */
    .invoice-notes {
        margin-top: 20pt;
        padding-top: 15pt;
        border-top: 1pt solid #dee2e6;
    }
    
    .invoice-notes h4 {
        font-size: 12pt;
        font-weight: bold;
        color: #730C02 !important;
        margin-bottom: 8pt;
    }
    
    .invoice-notes p {
        font-size: 10pt;
        color: #666 !important;
        line-height: 1.4;
    }
    
    /* تذييل الفاتورة */
    .invoice-footer {
        margin-top: 30pt;
        text-align: center;
        font-size: 9pt;
        color: #666 !important;
        border-top: 1pt solid #dee2e6;
        padding-top: 15pt;
    }
    
    /* تنسيق التقارير */
    .report {
        width: 100%;
        margin: 0;
        padding: 15pt;
    }
    
    .report-header {
        text-align: center;
        margin-bottom: 20pt;
        padding-bottom: 15pt;
        border-bottom: 2pt solid #730C02;
    }
    
    .report-header h1 {
        font-size: 18pt;
        font-weight: bold;
        color: #730C02 !important;
        margin-bottom: 5pt;
    }
    
    .report-header p {
        font-size: 12pt;
        color: #333 !important;
    }
    
    .report-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 15pt;
        font-size: 9pt;
    }
    
    .report-table th {
        background: #f8f9fa !important;
        color: #730C02 !important;
        padding: 6pt;
        text-align: center;
        font-weight: bold;
        border: 1pt solid #dee2e6;
    }
    
    .report-table td {
        padding: 4pt 6pt;
        text-align: center;
        border: 1pt solid #dee2e6;
        color: #333 !important;
    }
    
    /* تنسيق الإيصالات */
    .receipt {
        width: 58mm;
        margin: 0 auto;
        padding: 5pt;
        font-size: 8pt;
        text-align: center;
    }
    
    .receipt-header {
        margin-bottom: 10pt;
        padding-bottom: 5pt;
        border-bottom: 1pt dashed #333;
    }
    
    .receipt-header h1 {
        font-size: 12pt;
        font-weight: bold;
        color: #000 !important;
        margin-bottom: 2pt;
    }
    
    .receipt-items {
        text-align: right;
        margin-bottom: 10pt;
    }
    
    .receipt-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2pt;
        font-size: 7pt;
    }
    
    .receipt-total {
        border-top: 1pt dashed #333;
        padding-top: 5pt;
        font-weight: bold;
        font-size: 9pt;
    }
    
    .receipt-footer {
        margin-top: 10pt;
        padding-top: 5pt;
        border-top: 1pt dashed #333;
        font-size: 6pt;
        text-align: center;
    }
    
    /* تحسينات عامة للطباعة */
    .card {
        box-shadow: none !important;
        border: 1pt solid #dee2e6 !important;
        background: #fff !important;
        page-break-inside: avoid;
    }
    
    .table {
        background: #fff !important;
        color: #000 !important;
    }
    
    .table th {
        background: #f8f9fa !important;
        color: #000 !important;
    }
    
    .table td {
        color: #000 !important;
    }
    
    /* منع تقسيم العناصر */
    .no-break {
        page-break-inside: avoid;
    }
    
    /* فواصل الصفحات */
    .page-break {
        page-break-before: always;
    }
    
    .page-break-after {
        page-break-after: always;
    }
    
    /* تنسيق الأرقام */
    .number {
        font-family: 'Courier New', monospace !important;
        direction: ltr !important;
        text-align: left !important;
    }
    
    /* تنسيق التواريخ */
    .date {
        font-family: 'Courier New', monospace !important;
        direction: ltr !important;
    }
    
    /* تحسين جودة الطباعة */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
    
    /* تنسيق الباركود */
    .barcode {
        text-align: center;
        margin: 10pt 0;
    }
    
    .barcode img {
        max-height: 30pt;
    }
    
    /* تنسيق QR Code */
    .qr-code {
        text-align: center;
        margin: 10pt 0;
    }
    
    .qr-code img {
        max-width: 50pt;
        max-height: 50pt;
    }
}
