/**
 * Cybernet Magasin - إدارة العملاء
 * المطور: salim
 * الترخيص: مجاني ومفتوح المصدر
 */

class CustomersManager {
    constructor() {
        this.customers = [];
        this.filteredCustomers = [];
        this.currentCustomer = null;
        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.init();
    }

    // تهيئة مدير العملاء
    init() {
        this.loadData();
    }

    // تحميل البيانات
    loadData() {
        this.customers = db.getCustomers();
        this.filteredCustomers = [...this.customers];
    }

    // عرض واجهة إدارة العملاء
    render() {
        const container = DOMUtils.$('#customers-page');
        if (!container) return;

        this.loadData();

        container.innerHTML = `
            <div class="customers-container">
                <!-- شريط الأدوات -->
                <div class="customers-toolbar">
                    <div class="toolbar-left">
                        <h2>إدارة العملاء</h2>
                        <span class="customers-count">${this.customers.length} عميل</span>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-primary" onclick="customersManager.showAddCustomerModal()">
                            <i class="fas fa-plus"></i>
                            إضافة عميل جديد
                        </button>
                        <button class="btn btn-info" onclick="customersManager.exportCustomers()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="btn btn-warning" onclick="customersManager.showDebtorsReport()">
                            <i class="fas fa-exclamation-triangle"></i>
                            تقرير الديون
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="customers-filters">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <div class="search-box">
                            <input type="text" id="customers-search" placeholder="ابحث بالاسم، الهاتف، أو العنوان...">
                            <button onclick="customersManager.searchCustomers()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" onchange="customersManager.filterByStatus()">
                            <option value="">الكل</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="debtor">مدين</option>
                            <option value="creditor">دائن</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>ترتيب حسب:</label>
                        <select id="sort-by" onchange="customersManager.sortCustomers()">
                            <option value="name">الاسم</option>
                            <option value="balance">الرصيد</option>
                            <option value="createdAt">تاريخ الإضافة</option>
                        </select>
                        <button class="sort-order-btn" onclick="customersManager.toggleSortOrder()">
                            <i class="fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}"></i>
                        </button>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-sm btn-secondary" onclick="customersManager.clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>

                <!-- جدول العملاء -->
                <div class="customers-table-container">
                    <table class="table customers-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" onchange="customersManager.toggleSelectAll()">
                                </th>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                            ${this.renderCustomersTable()}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <div class="pagination-container">
                    ${this.renderPagination()}
                </div>

                <!-- الإجراءات المجمعة -->
                <div class="bulk-actions" id="bulk-actions" style="display: none;">
                    <div class="bulk-actions-content">
                        <span id="selected-count">0 عميل محدد</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-sm btn-warning" onclick="customersManager.bulkToggleStatus()">
                                <i class="fas fa-toggle-on"></i>
                                تبديل الحالة
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="customersManager.bulkDelete()">
                                <i class="fas fa-trash"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل عميل -->
            <div id="customer-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="customer-modal-title">إضافة عميل جديد</h3>
                        <button class="close-btn" onclick="closeModal('customer-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="customer-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم العميل *</label>
                                    <input type="text" id="customer-name" required>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف</label>
                                    <input type="tel" id="customer-phone">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>البريد الإلكتروني</label>
                                    <input type="email" id="customer-email">
                                </div>
                                <div class="form-group">
                                    <label>الحد الائتماني</label>
                                    <input type="number" id="customer-credit-limit" min="0" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>العنوان</label>
                                <textarea id="customer-address" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="customer-notes" rows="2"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="customer-active" checked>
                                    <span>عميل نشط</span>
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('customer-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="customersManager.saveCustomer()">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- نافذة عرض تفاصيل العميل -->
            <div id="customer-details-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل العميل</h3>
                        <button class="close-btn" onclick="closeModal('customer-details-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body" id="customer-details-content">
                        <!-- سيتم تحميل التفاصيل هنا -->
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الدفعات -->
            <div id="payment-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="payment-modal-title">تسجيل دفعة</h3>
                        <button class="close-btn" onclick="closeModal('payment-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="payment-form">
                            <div class="form-group">
                                <label>العميل:</label>
                                <input type="text" id="payment-customer-name" readonly>
                            </div>
                            <div class="form-group">
                                <label>الرصيد الحالي:</label>
                                <input type="text" id="payment-current-balance" readonly>
                            </div>
                            <div class="form-group">
                                <label>نوع العملية *</label>
                                <select id="payment-type" required onchange="customersManager.updatePaymentType()">
                                    <option value="">اختر نوع العملية</option>
                                    <option value="payment">دفعة (تقليل الدين)</option>
                                    <option value="charge">إضافة مبلغ (زيادة الدين)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>المبلغ *</label>
                                <input type="number" id="payment-amount" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label>طريقة الدفع</label>
                                <select id="payment-method">
                                    <option value="cash">نقداً</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="payment-notes" rows="2"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('payment-modal')">إلغاء</button>
                        <button class="btn btn-primary" onclick="customersManager.savePayment()">حفظ</button>
                    </div>
                </div>
            </div>

            <!-- نافذة تقرير الديون -->
            <div id="debtors-modal" class="modal">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تقرير الديون</h3>
                        <button class="close-btn" onclick="closeModal('debtors-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="debtors-report-content">
                            <!-- سيتم تحميل التقرير هنا -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal('debtors-modal')">إغلاق</button>
                        <button class="btn btn-primary" onclick="customersManager.printDebtorsReport()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // البحث في العملاء
        const searchInput = DOMUtils.$('#customers-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchCustomers(e.target.value);
            });
        }

        // مستمعي أحداث الجدول
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('customer-checkbox')) {
                this.updateBulkActions();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (window.app.currentPage === 'customers') {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'n':
                            e.preventDefault();
                            this.showAddCustomerModal();
                            break;
                        case 'f':
                            e.preventDefault();
                            searchInput?.focus();
                            break;
                    }
                }
            }
        });
    }

    // عرض جدول العملاء
    renderCustomersTable() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageCustomers = this.filteredCustomers.slice(startIndex, endIndex);

        if (pageCustomers.length === 0) {
            return `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="no-data">
                            <i class="fas fa-users"></i>
                            <p>لا يوجد عملاء</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return pageCustomers.map(customer => {
            const isDebtor = customer.balance > 0;
            const isCreditor = customer.balance < 0;
            
            return `
                <tr class="customer-row ${!customer.active ? 'inactive' : ''}" data-customer-id="${customer.id}">
                    <td>
                        ${customer.isDefault ? '' : '<input type="checkbox" class="customer-checkbox" value="' + customer.id + '">'}
                    </td>
                    <td>
                        <div class="customer-name-cell">
                            <strong>${customer.name}</strong>
                            ${customer.isDefault ? '<span class="default-badge">افتراضي</span>' : ''}
                        </div>
                    </td>
                    <td>${customer.phone || '-'}</td>
                    <td>${customer.email || '-'}</td>
                    <td class="balance-cell">
                        <span class="balance ${isDebtor ? 'debtor' : isCreditor ? 'creditor' : 'zero'}">
                            ${NumberUtils.formatCurrency(Math.abs(customer.balance))}
                            ${isDebtor ? ' (مدين)' : isCreditor ? ' (دائن)' : ''}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${customer.active ? 'active' : 'inactive'}">
                            ${customer.active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>${DateUtils.formatDate(customer.createdAt)}</td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="customersManager.viewCustomer('${customer.id}')" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${!customer.isDefault ? `
                                <button class="btn btn-sm btn-primary" onclick="customersManager.editCustomer('${customer.id}')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="customersManager.showPaymentModal('${customer.id}')" title="دفعة">
                                    <i class="fas fa-money-bill"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="customersManager.toggleCustomerStatus('${customer.id}')" title="تبديل الحالة">
                                    <i class="fas fa-toggle-${customer.active ? 'on' : 'off'}"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="customersManager.deleteCustomer('${customer.id}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    // عرض التنقل بين الصفحات
    renderPagination() {
        const totalPages = Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        
        if (totalPages <= 1) return '';

        let pagination = '<div class="pagination">';
        
        // الصفحة السابقة
        if (this.currentPage > 1) {
            pagination += `<button class="page-btn" onclick="customersManager.goToPage(${this.currentPage - 1})">السابق</button>`;
        }
        
        // أرقام الصفحات
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                pagination += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination += `<button class="page-btn" onclick="customersManager.goToPage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination += '<span class="page-dots">...</span>';
            }
        }
        
        // الصفحة التالية
        if (this.currentPage < totalPages) {
            pagination += `<button class="page-btn" onclick="customersManager.goToPage(${this.currentPage + 1})">التالي</button>`;
        }
        
        pagination += '</div>';
        
        return pagination;
    }

    // البحث في العملاء
    searchCustomers(query = '') {
        const searchInput = DOMUtils.$('#customers-search');
        const searchQuery = query || searchInput?.value || '';

        if (searchQuery.trim() === '') {
            this.filteredCustomers = [...this.customers];
        } else {
            const searchTerm = searchQuery.toLowerCase();
            this.filteredCustomers = this.customers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                (customer.phone && customer.phone.includes(searchTerm)) ||
                (customer.email && customer.email.toLowerCase().includes(searchTerm)) ||
                (customer.address && customer.address.toLowerCase().includes(searchTerm))
            );
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // فلترة حسب الحالة
    filterByStatus() {
        const statusSelect = DOMUtils.$('#status-filter');
        const status = statusSelect?.value;

        switch (status) {
            case 'active':
                this.filteredCustomers = this.customers.filter(c => c.active);
                break;
            case 'inactive':
                this.filteredCustomers = this.customers.filter(c => !c.active);
                break;
            case 'debtor':
                this.filteredCustomers = this.customers.filter(c => c.balance > 0);
                break;
            case 'creditor':
                this.filteredCustomers = this.customers.filter(c => c.balance < 0);
                break;
            default:
                this.filteredCustomers = [...this.customers];
        }

        this.currentPage = 1;
        this.updateTable();
    }

    // ترتيب العملاء
    sortCustomers() {
        const sortSelect = DOMUtils.$('#sort-by');
        this.sortBy = sortSelect?.value || 'name';

        this.filteredCustomers.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            // معالجة خاصة للتواريخ
            if (this.sortBy === 'createdAt') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            }

            // معالجة خاصة للأرقام
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return this.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // معالجة النصوص
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
            }
        });

        this.updateTable();
    }

    // تبديل ترتيب الفرز
    toggleSortOrder() {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        const sortBtn = DOMUtils.$('.sort-order-btn i');
        if (sortBtn) {
            sortBtn.className = `fas fa-sort-${this.sortOrder === 'asc' ? 'up' : 'down'}`;
        }
        this.sortCustomers();
    }

    // مسح الفلاتر
    clearFilters() {
        DOMUtils.$('#customers-search').value = '';
        DOMUtils.$('#status-filter').value = '';
        DOMUtils.$('#sort-by').value = 'name';

        this.sortBy = 'name';
        this.sortOrder = 'asc';
        this.filteredCustomers = [...this.customers];
        this.currentPage = 1;

        this.updateTable();
    }

    // الانتقال لصفحة معينة
    goToPage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الجدول
    updateTable() {
        const tableBody = DOMUtils.$('#customers-table-body');
        const paginationContainer = DOMUtils.$('.pagination-container');
        const customersCount = DOMUtils.$('.customers-count');

        if (tableBody) {
            tableBody.innerHTML = this.renderCustomersTable();
        }

        if (paginationContainer) {
            paginationContainer.innerHTML = this.renderPagination();
        }

        if (customersCount) {
            customersCount.textContent = `${this.filteredCustomers.length} عميل`;
        }

        this.updateBulkActions();
    }

    // إظهار نافذة إضافة عميل
    showAddCustomerModal() {
        this.currentCustomer = null;
        this.resetCustomerForm();
        DOMUtils.$('#customer-modal-title').textContent = 'إضافة عميل جديد';
        DOMUtils.addClass('#customer-modal', 'show');

        setTimeout(() => {
            DOMUtils.$('#customer-name')?.focus();
        }, 100);
    }

    // تعديل عميل
    editCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.isDefault) return;

        this.currentCustomer = customer;
        this.fillCustomerForm(customer);
        DOMUtils.$('#customer-modal-title').textContent = 'تعديل العميل';
        DOMUtils.addClass('#customer-modal', 'show');
    }

    // عرض تفاصيل العميل
    viewCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer) return;

        // الحصول على تاريخ المعاملات
        const sales = db.getSales().filter(sale => sale.customerId === customerId);
        const payments = db.get('cm_payments', []).filter(payment => payment.customerId === customerId);

        const detailsContent = DOMUtils.$('#customer-details-content');

        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="customer-details">
                    <div class="customer-header">
                        <div class="customer-basic-info">
                            <h3>${customer.name}</h3>
                            <div class="customer-balance ${customer.balance > 0 ? 'debtor' : customer.balance < 0 ? 'creditor' : 'zero'}">
                                <strong>الرصيد: ${NumberUtils.formatCurrency(Math.abs(customer.balance))}</strong>
                                ${customer.balance > 0 ? ' (مدين)' : customer.balance < 0 ? ' (دائن)' : ''}
                            </div>
                        </div>
                        <div class="customer-actions">
                            ${!customer.isDefault ? `
                                <button class="btn btn-primary" onclick="customersManager.editCustomer('${customer.id}')">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                                <button class="btn btn-success" onclick="customersManager.showPaymentModal('${customer.id}')">
                                    <i class="fas fa-money-bill"></i>
                                    دفعة
                                </button>
                            ` : ''}
                        </div>
                    </div>

                    <div class="customer-info-grid">
                        <div class="info-section">
                            <h4>معلومات الاتصال</h4>
                            <div class="info-item">
                                <label>الهاتف:</label>
                                <span>${customer.phone || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>البريد الإلكتروني:</label>
                                <span>${customer.email || 'غير محدد'}</span>
                            </div>
                            <div class="info-item">
                                <label>العنوان:</label>
                                <span>${customer.address || 'غير محدد'}</span>
                            </div>
                        </div>

                        <div class="info-section">
                            <h4>معلومات الحساب</h4>
                            <div class="info-item">
                                <label>الحد الائتماني:</label>
                                <span>${NumberUtils.formatCurrency(customer.creditLimit || 0)}</span>
                            </div>
                            <div class="info-item">
                                <label>الحالة:</label>
                                <span class="status-badge ${customer.active ? 'active' : 'inactive'}">
                                    ${customer.active ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                            <div class="info-item">
                                <label>تاريخ الإضافة:</label>
                                <span>${DateUtils.formatDate(customer.createdAt, 'dd/mm/yyyy hh:mm')}</span>
                            </div>
                        </div>
                    </div>

                    ${customer.notes ? `
                        <div class="customer-notes">
                            <h4>ملاحظات</h4>
                            <p>${customer.notes}</p>
                        </div>
                    ` : ''}

                    <div class="customer-transactions">
                        <h4>آخر المعاملات</h4>
                        <div class="transactions-list">
                            ${this.renderCustomerTransactions(sales, payments)}
                        </div>
                    </div>
                </div>
            `;
        }

        DOMUtils.addClass('#customer-details-modal', 'show');
    }

    // عرض معاملات العميل
    renderCustomerTransactions(sales, payments) {
        const allTransactions = [];

        // إضافة المبيعات
        sales.forEach(sale => {
            allTransactions.push({
                type: 'sale',
                date: sale.createdAt,
                amount: sale.total,
                description: `فاتورة بيع #${sale.saleNumber}`,
                paymentMethod: sale.paymentMethod
            });
        });

        // إضافة الدفعات
        payments.forEach(payment => {
            allTransactions.push({
                type: payment.type,
                date: payment.createdAt,
                amount: payment.amount,
                description: payment.notes || (payment.type === 'payment' ? 'دفعة' : 'إضافة مبلغ'),
                paymentMethod: payment.method
            });
        });

        // ترتيب حسب التاريخ
        allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        if (allTransactions.length === 0) {
            return '<p class="text-center text-secondary">لا توجد معاملات</p>';
        }

        return allTransactions.slice(0, 10).map(transaction => `
            <div class="transaction-item ${transaction.type}">
                <div class="transaction-icon">
                    <i class="fas fa-${transaction.type === 'sale' ? 'shopping-cart' : transaction.type === 'payment' ? 'arrow-down' : 'arrow-up'}"></i>
                </div>
                <div class="transaction-content">
                    <h5>${transaction.description}</h5>
                    <p>
                        ${DateUtils.formatDate(transaction.date, 'dd/mm/yyyy hh:mm')} |
                        ${transaction.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}
                    </p>
                </div>
                <div class="transaction-amount ${transaction.type}">
                    ${transaction.type === 'payment' ? '-' : '+'}${NumberUtils.formatCurrency(transaction.amount)}
                </div>
            </div>
        `).join('');
    }

    // حذف عميل
    deleteCustomer(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.isDefault) return;

        // التحقق من وجود معاملات
        const sales = db.getSales().filter(sale => sale.customerId === customerId);
        if (sales.length > 0) {
            app.showNotification('لا يمكن حذف العميل لوجود معاملات مرتبطة به', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف العميل "${customer.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                if (db.deleteCustomer(customerId)) {
                    app.showNotification('تم حذف العميل بنجاح', 'success');
                    this.loadData();
                    this.updateTable();
                } else {
                    app.showNotification('فشل في حذف العميل', 'error');
                }
            }
        );
    }

    // تبديل حالة العميل
    toggleCustomerStatus(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.isDefault) return;

        const updatedCustomer = { active: !customer.active };

        if (db.updateCustomer(customerId, updatedCustomer)) {
            app.showNotification(
                `تم ${updatedCustomer.active ? 'تفعيل' : 'إلغاء تفعيل'} العميل`,
                'success'
            );
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في تحديث حالة العميل', 'error');
        }
    }

    // إعادة تعيين نموذج العميل
    resetCustomerForm() {
        const form = DOMUtils.$('#customer-form');
        if (form) {
            form.reset();
        }

        DOMUtils.$('#customer-active').checked = true;
        DOMUtils.$('#customer-credit-limit').value = '0';
    }

    // ملء نموذج العميل
    fillCustomerForm(customer) {
        DOMUtils.$('#customer-name').value = customer.name || '';
        DOMUtils.$('#customer-phone').value = customer.phone || '';
        DOMUtils.$('#customer-email').value = customer.email || '';
        DOMUtils.$('#customer-address').value = customer.address || '';
        DOMUtils.$('#customer-credit-limit').value = customer.creditLimit || 0;
        DOMUtils.$('#customer-notes').value = customer.notes || '';
        DOMUtils.$('#customer-active').checked = customer.active !== false;
    }

    // حفظ العميل
    saveCustomer() {
        const name = DOMUtils.$('#customer-name')?.value?.trim();
        const phone = DOMUtils.$('#customer-phone')?.value?.trim();
        const email = DOMUtils.$('#customer-email')?.value?.trim();
        const address = DOMUtils.$('#customer-address')?.value?.trim();
        const creditLimit = parseFloat(DOMUtils.$('#customer-credit-limit')?.value || 0);
        const notes = DOMUtils.$('#customer-notes')?.value?.trim();
        const active = DOMUtils.$('#customer-active')?.checked;

        // التحقق من صحة البيانات
        const validation = this.validateCustomerData({
            name, phone, email, creditLimit
        });

        if (!validation.valid) {
            app.showNotification(validation.message, 'error');
            return;
        }

        const customerData = {
            name,
            phone,
            email,
            address,
            creditLimit,
            notes,
            active
        };

        let success = false;

        if (this.currentCustomer) {
            // تحديث عميل موجود
            success = db.updateCustomer(this.currentCustomer.id, customerData);
        } else {
            // إضافة عميل جديد
            success = db.addCustomer(customerData);
        }

        if (success) {
            app.showNotification(
                this.currentCustomer ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح',
                'success'
            );

            app.closeModal('customer-modal');
            this.loadData();
            this.updateTable();
        } else {
            app.showNotification('فشل في حفظ العميل', 'error');
        }
    }

    // التحقق من صحة بيانات العميل
    validateCustomerData(data) {
        if (!data.name) {
            return { valid: false, message: 'اسم العميل مطلوب' };
        }

        if (data.email && !StringUtils.isValidEmail(data.email)) {
            return { valid: false, message: 'البريد الإلكتروني غير صحيح' };
        }

        if (data.phone && !StringUtils.isValidPhone(data.phone)) {
            return { valid: false, message: 'رقم الهاتف غير صحيح' };
        }

        if (data.creditLimit < 0) {
            return { valid: false, message: 'الحد الائتماني لا يمكن أن يكون سالباً' };
        }

        return { valid: true };
    }

    // إظهار نافذة الدفعات
    showPaymentModal(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        if (!customer || customer.isDefault) return;

        this.currentCustomer = customer;

        // ملء البيانات الأساسية
        DOMUtils.$('#payment-customer-name').value = customer.name;
        DOMUtils.$('#payment-current-balance').value = NumberUtils.formatCurrency(customer.balance);

        // إعادة تعيين النموذج
        DOMUtils.$('#payment-type').value = '';
        DOMUtils.$('#payment-amount').value = '';
        DOMUtils.$('#payment-method').value = 'cash';
        DOMUtils.$('#payment-notes').value = '';

        DOMUtils.addClass('#payment-modal', 'show');
    }

    // تحديث نوع الدفعة
    updatePaymentType() {
        const paymentType = DOMUtils.$('#payment-type')?.value;
        const modalTitle = DOMUtils.$('#payment-modal-title');

        if (paymentType === 'payment') {
            modalTitle.textContent = 'تسجيل دفعة (تقليل الدين)';
        } else if (paymentType === 'charge') {
            modalTitle.textContent = 'إضافة مبلغ (زيادة الدين)';
        } else {
            modalTitle.textContent = 'تسجيل دفعة';
        }
    }

    // حفظ الدفعة
    savePayment() {
        const paymentType = DOMUtils.$('#payment-type')?.value;
        const amount = parseFloat(DOMUtils.$('#payment-amount')?.value || 0);
        const method = DOMUtils.$('#payment-method')?.value;
        const notes = DOMUtils.$('#payment-notes')?.value?.trim();

        if (!paymentType) {
            app.showNotification('يرجى اختيار نوع العملية', 'error');
            return;
        }

        if (amount <= 0) {
            app.showNotification('المبلغ يجب أن يكون أكبر من صفر', 'error');
            return;
        }

        // حساب الرصيد الجديد
        let newBalance = this.currentCustomer.balance;
        if (paymentType === 'payment') {
            newBalance -= amount; // تقليل الدين
        } else {
            newBalance += amount; // زيادة الدين
        }

        // تحديث رصيد العميل
        if (db.updateCustomer(this.currentCustomer.id, { balance: newBalance })) {
            // حفظ سجل الدفعة
            const payment = {
                customerId: this.currentCustomer.id,
                type: paymentType,
                amount: amount,
                method: method,
                notes: notes,
                createdAt: new Date().toISOString()
            };

            const payments = db.get('cm_payments', []);
            payments.push(payment);
            db.save('cm_payments', payments);

            app.showNotification('تم تسجيل العملية بنجاح', 'success');
            app.closeModal('payment-modal');

            this.loadData();
            this.updateTable();

            // تحديث لوحة المعلومات
            if (window.dashboard) {
                window.dashboard.updateStats();
            }
        } else {
            app.showNotification('فشل في تسجيل العملية', 'error');
        }
    }

    // تحديد/إلغاء تحديد الكل
    toggleSelectAll() {
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const customerCheckboxes = DOMUtils.$$('.customer-checkbox');

        customerCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateBulkActions();
    }

    // تحديث الإجراءات المجمعة
    updateBulkActions() {
        const selectedCheckboxes = DOMUtils.$$('.customer-checkbox:checked');
        const bulkActions = DOMUtils.$('#bulk-actions');
        const selectedCount = DOMUtils.$('#selected-count');

        if (selectedCheckboxes.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = `${selectedCheckboxes.length} عميل محدد`;
        } else {
            bulkActions.style.display = 'none';
        }

        // تحديث حالة "تحديد الكل"
        const selectAllCheckbox = DOMUtils.$('#select-all');
        const allCheckboxes = DOMUtils.$$('.customer-checkbox');

        if (selectedCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // تبديل حالة العملاء المحددين
    bulkToggleStatus() {
        const selectedIds = Array.from(DOMUtils.$$('.customer-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي عميل', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد العملية',
            `هل تريد تبديل حالة ${selectedIds.length} عميل؟`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    const customer = this.customers.find(c => c.id === id);
                    if (customer && !customer.isDefault) {
                        if (db.updateCustomer(id, { active: !customer.active })) {
                            successCount++;
                        }
                    }
                });

                app.showNotification(`تم تحديث ${successCount} عميل`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // حذف العملاء المحددين
    bulkDelete() {
        const selectedIds = Array.from(DOMUtils.$$('.customer-checkbox:checked')).map(cb => cb.value);

        if (selectedIds.length === 0) {
            app.showNotification('لم يتم تحديد أي عميل', 'warning');
            return;
        }

        // التحقق من وجود معاملات
        const sales = db.getSales();
        const hasTransactions = selectedIds.some(id =>
            sales.some(sale => sale.customerId === id)
        );

        if (hasTransactions) {
            app.showNotification('لا يمكن حذف عملاء لديهم معاملات', 'warning');
            return;
        }

        app.showConfirm(
            'تأكيد الحذف',
            `هل تريد حذف ${selectedIds.length} عميل؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                let successCount = 0;

                selectedIds.forEach(id => {
                    const customer = this.customers.find(c => c.id === id);
                    if (customer && !customer.isDefault) {
                        if (db.deleteCustomer(id)) {
                            successCount++;
                        }
                    }
                });

                app.showNotification(`تم حذف ${successCount} عميل`, 'success');
                this.loadData();
                this.updateTable();
            }
        );
    }

    // إظهار تقرير الديون
    showDebtorsReport() {
        const debtors = this.customers.filter(customer =>
            customer.balance > 0 && !customer.isDefault
        );

        const reportContent = DOMUtils.$('#debtors-report-content');

        if (reportContent) {
            const totalDebt = debtors.reduce((sum, customer) => sum + customer.balance, 0);

            reportContent.innerHTML = `
                <div class="debtors-report">
                    <div class="report-summary">
                        <h4>ملخص الديون</h4>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <label>عدد العملاء المدينين:</label>
                                <span>${debtors.length}</span>
                            </div>
                            <div class="stat-item">
                                <label>إجمالي الديون:</label>
                                <span class="debt-amount">${NumberUtils.formatCurrency(totalDebt)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="debtors-table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>الهاتف</th>
                                    <th>المبلغ المستحق</th>
                                    <th>تاريخ آخر معاملة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${debtors.map(customer => {
                                    const sales = db.getSales().filter(sale => sale.customerId === customer.id);
                                    const lastSale = sales.length > 0 ?
                                        sales.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0] : null;

                                    return `
                                        <tr>
                                            <td>${customer.name}</td>
                                            <td>${customer.phone || '-'}</td>
                                            <td class="debt-amount">${NumberUtils.formatCurrency(customer.balance)}</td>
                                            <td>${lastSale ? DateUtils.formatDate(lastSale.createdAt) : '-'}</td>
                                            <td>
                                                <button class="btn btn-sm btn-success" onclick="customersManager.showPaymentModal('${customer.id}')">
                                                    <i class="fas fa-money-bill"></i>
                                                    دفعة
                                                </button>
                                            </td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        DOMUtils.addClass('#debtors-modal', 'show');
    }

    // طباعة تقرير الديون
    printDebtorsReport() {
        window.print();
    }

    // تصدير العملاء
    exportCustomers() {
        const data = this.filteredCustomers.map(customer => ({
            'الاسم': customer.name,
            'الهاتف': customer.phone || '',
            'البريد الإلكتروني': customer.email || '',
            'العنوان': customer.address || '',
            'الرصيد': customer.balance,
            'الحد الائتماني': customer.creditLimit || 0,
            'الحالة': customer.active ? 'نشط' : 'غير نشط',
            'تاريخ الإضافة': DateUtils.formatDate(customer.createdAt),
            'ملاحظات': customer.notes || ''
        }));

        const csvContent = this.convertToCSV(data);
        const filename = `customers-${DateUtils.getCurrentDate()}.csv`;

        Utils.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
        app.showNotification('تم تصدير العملاء بنجاح', 'success');
    }

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [];

        // إضافة العناوين
        csvRows.push(headers.join(','));

        // إضافة البيانات
        data.forEach(row => {
            const values = headers.map(header => {
                const value = row[header] || '';
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        });

        return csvRows.join('\n');
    }
}

// إنشاء مثيل من مدير العملاء
const customersManager = new CustomersManager();

// تصدير مدير العملاء للاستخدام العام
window.customersManager = customersManager;
